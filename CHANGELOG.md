# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### Types of changes
- **Added** - for new features. 
- **Changed** - for changes in existing functionality.
- **Deprecated** - for soon-to-be removed features.
- **Removed** - for now removed features.
- **Fixed** - for any bug fixes.
- **Security** - in case of vulnerabilities.
- **Updated** - for any version updates.
- **Refactoring** - code refactoring.
- **Testing** - code testing.

## [Unreleased]
### Added
- "Credit" tag for Digital invoices [Jira #FL-13471](https://futurelog.atlassian.net/browse/FL-13471)
- Search by match filter for recievings [Jira #FL-9988](https://futurelog.atlassian.net/browse/FL-9988)
### Fixed
- Fixed title on Approve invoice (details) / Archive invoices (details) page [Jira #FL-12748](https://futurelog.atlassian.net/browse/FL-12748)
### Changed
- Block 'Continue' button on 'User Organization Login' screen if field have error [Jira #FL-12648](https://futurelog.atlassian.net/browse/FL-12648)

## [3.15.4] - 2025-06-05
### Added
- Additional Receiving Order Item editing restrictions [Jira #FL-13485](https://futurelog.atlassian.net/browse/FL-13485)
- "Add all receiving items" for Receiving [Jira #FL-13484](https://futurelog.atlassian.net/browse/FL-13484)
- Order history for a product in Orders List [Jira #FL-11086](https://futurelog.atlassian.net/browse/FL-11086)
- Order history for a product in Purchase Requests [Jira #FL-11084](https://futurelog.atlassian.net/browse/FL-11084)
- Delivery note number to Process Receiving Details screen [Jira #FL-12578](https://futurelog.atlassian.net/browse/FL-12578)
### Fixed
- Next approver selection for Purchase Requests [Jira #FL-13293](https://futurelog.atlassian.net/browse/FL-13293)
- Category and Inventory Unit fields visibility for Receiving Order Item [Jira #FL-7830](https://futurelog.atlassian.net/browse/FL-7830)
- Universal approver looses permissions in some cases after refreshing session
- Booking without item price error [Jira #FL-13713](https://futurelog.atlassian.net/browse/FL-13713)

## [3.15.3] - 2025-06-12
### Fixed
- Case when app notification permission was revoked by the system, while user still has an active session [Jira #FL-13567](https://futurelog.atlassian.net/browse/FL-13567)

## [3.15.2] - 2025-05-30
### Added
- Ability to Reset, Confirm or Book Receiving from the list [Jira #FL-12982](https://futurelog.atlassian.net/browse/FL-12982)
### Changed
- Status notifications and badges UI/UX update [Jira #FL-12734](https://futurelog.atlassian.net/browse/FL-12734)
- Load translations only from production environment [Jira #FL-12200](https://futurelog.atlassian.net/browse/FL-12200)
### Fixed
- Add items from order button visibility condition on order items [Jira #FL-11796](https://futurelog.atlassian.net/browse/FL-11796)
- Wrong tab selection on Purchase Request screen [Jira #FL-11339](https://futurelog.atlassian.net/browse/FL-11339), [Jira #FL-12108](https://futurelog.atlassian.net/browse/FL-12108)
- Barcode scanning functionality on iOS [Jira #FL-13307](https://futurelog.atlassian.net/browse/FL-13307)
- Password reset screen [Jira #FL-13387](https://futurelog.atlassian.net/browse/FL-13387)

## [3.15.1] - 2025-05-26
### Fixed
- Pin confirmation screen [Jira #FL-13225](https://futurelog.atlassian.net/browse/FL-13225)

## [3.15.0] - 2025-05-15
### Added
- GTIN codes editing for products in Store Master Data
### Fixed
- exchange rate update after changing currency on freight cost [Jira #FL-12135](https://futurelog.atlassian.net/browse/FL-12135)
### Refactoring
- use Flutter Bloc repository providers instead of Service Locator concept
- replace `edit_distance` with `string_similarity`
- replace `flutter_app_badger` with `app_badge_plus`
- simplify API connectivity checks with `ApiConnectivityCubit` and `ApiConnectivityListener`
- `FuturelogApp` is now a stateless widget
## Updated
- `flutter_bloc` to 9.1.1
### Removed
- `bubble` package
- `get_it` package
- `NetworkBloc`, `NetworkState` and `NetworkStateChangedMixin`
- `OfflineMode` widget

## [3.14.0] - 2025-04-17
### Changed
- hide specific Suppliers from Receiving creation from [Jira #FL-12652](https://futurelog.atlassian.net/browse/FL-12652)
### Fixed
- default value for user preferences locale [Jira #FL-12781](https://futurelog.atlassian.net/browse/FL-12781)
### Refactoring
- Migrate to wide gamut Color
- refactor deprecated methods from 3rd part packages
- replace `pdf_render` with `pdfrx`
### Updated
- Flutter SDK to 3.29.3

## [3.13.1] - 2025-04-09
### Added
- MS Windows build workflow for Codemagic CI/CD
- SSO login support [Jira #FL-9466](https://futurelog.atlassian.net/browse/FL-9466)
### Removed
- `rive` animation package and related assets

## [3.13.0] - 2025-03-21
### Added
- In-app photo taking functionality
- Barcode scanning functionality with on-device ML kit
- Android project namespace
- Gradle plugin for namespace setting for dependencies incompatible with Gradle 8
- New App menu [FL-8211](https://futurelog.atlassian.net/browse/FL-8211)
### Fixed
- Wrong tab bar layout on Order screen in Orders Archive [Jira #FL-10633](https://futurelog.atlassian.net/browse/FL-10633)
- Bulk Discount icon lock condition for approved articles on Shopping Cart [Jira #FL-10095](https://futurelog.atlassian.net/browse/FL-10095)
- Text auto selection for Discount value input on Shopping Cart [Jira #FL-10104](https://futurelog.atlassian.net/browse/FL-10104)
- CAPEX permissions [Jira #FL-10530](https://futurelog.atlassian.net/browse/FL-10530)
### Changed
- Take photos for attachments with in-app photo taking functionality
- Barcode scanning with in-app photo taking functionality
- Menu labels [FL-8210](https://futurelog.atlassian.net/browse/FL-8210)
### Updated
- Minimum iOS version from 12 to 15.5
- Kotlin dependency to 1.9.0
- com.android.application Gradle plugin to 8.9.0
- Gradle Wrapper to 8.11.1
- JVM requirement to version 17
### Removed
- `image_picker` package to have only one file picking package
- `barcode_scanner` package
### Testing
- `test` CI jobs can now be automatically interrupted when new job on the same git reference started

## [3.12.8] - 2025-03-18
### Added
- Hide External Comments functionality when it is disabled for Division [Jira #FL-11898](https://futurelog.atlassian.net/browse/FL-11898)

## [3.12.6] - 2025-02-06
### Added
- External comment separation by cost centers [Jira #FL-11846](https://futurelog.atlassian.net/browse/FL-11846), [Jira #FL-11902](https://futurelog.atlassian.net/browse/FL-11902), [Jira #FL-11904](https://futurelog.atlassian.net/browse/FL-11904)
### Fixed
- Login form button behavior [Jira #FL-11931](https://futurelog.atlassian.net/browse/FL-11931)

## [3.12.5] - 2025-02-06
### Added
- Delivery Note export to Medius status indication and Re-export [Jira #FL-11632](https://futurelog.atlassian.net/browse/FL-11632)
### Changed
- Allow sending orders from order lists when not all items has target stock [Jira #FL-11520](https://futurelog.atlassian.net/browse/FL-11520)
### Fixed
- Catalog price rages handling [Jira #FL-11217](https://futurelog.atlassian.net/browse/FL-11217), [Jira #FL-11577](https://futurelog.atlassian.net/browse/FL-11577)
- Authorize Orders: displays tab 'Budget' for PR from division without 'Budget' functionality [Jira #FL-9717](https://futurelog.atlassian.net/browse/FL-9717)
- Cart totals update after certain actions [Jira #FL-10173](https://futurelog.atlassian.net/browse/FL-10173)
- user navigation after invoice upload [Jira #FL-11302](https://futurelog.atlassian.net/browse/FL-11302)
- Receiving order item errors and warnings [Jira #FL-11797](https://futurelog.atlassian.net/browse/FL-11797)

## [3.12.4] - 2025-01-15
### Added
- CAPEX email links handling with SSO [Jira #FL-10356](https://futurelog.atlassian.net/browse/FL-10356)
- email link auto-login support [Jira #FL-10355](https://futurelog.atlassian.net/browse/FL-10355)
### Changed
- disable default Flutter deep links functionality
### Refactoring
- replace discontinued `uni_links` package with `app_links`

## [3.12.3] - 2025-01-02
### Added
- Cantonese language support [Jira #FL-9816](https://futurelog.atlassian.net/browse/FL-9816)
### Changed
- universal Add to Cart button UI/UX update with quantity in Cart [Jira #FL-2951](https://futurelog.atlassian.net/browse/FL-2951)
- allow 3 decimal places for product quantity [Jira #FL-10727](https://futurelog.atlassian.net/browse/FL-10727)
- navigate user to Scanned Documents screen after successful upload from Invoice Archive [Jira #FL-10114](https://futurelog.atlassian.net/browse/FL-10114)
- Transfer Orders Archive is now read-only [Jira #FL-10000](https://futurelog.atlassian.net/browse/FL-10000)
### Fixed
- cross-property budgets for Purchase Requests [Jira #FL-10132](https://futurelog.atlassian.net/browse/FL-10132)
- cross-property notifications handling [Jira #FL-10161](https://futurelog.atlassian.net/browse/FL-10161)
- search app bar autofocus [Jira #FL-9432](https://futurelog.atlassian.net/browse/FL-9432)
- Shopping Cart tab bars width [Jira #FL-9613](https://futurelog.atlassian.net/browse/FL-9613)
- Cart global status update on product delete [Jira #FL-10173](https://futurelog.atlassian.net/browse/FL-10173)
- Order list products refresh [Jira #FL-10370](https://futurelog.atlassian.net/browse/FL-10370)
- Purchase Request products refresh after other offers screen [Jira #FL-11051](https://futurelog.atlassian.net/browse/FL-11051)
- Provisional Booking processing status display [Jira #FL-9070](https://futurelog.atlassian.net/browse/FL-9070)
- missing Booking Item error messages [Jira #FL-11110](https://futurelog.atlassian.net/browse/FL-11110)
- Receiving delivery items addition [Jira #FL-10636](https://futurelog.atlassian.net/browse/FL-10636)
- booking results when confirming the receiving right after creation [Jira #FL-10619](https://futurelog.atlassian.net/browse/FL-10619)
- in-approval receiving editing [Jira #FL-10203](https://futurelog.atlassian.net/browse/FL-10203)
- external comment indication in Order Archive [Jira #FL-10632](https://futurelog.atlassian.net/browse/FL-10632)
- external comment last changed info in Shopping Cart Orders sending [Jira #FL-10809](https://futurelog.atlassian.net/browse/FL-10809)

## [3.12.2] - 2024-12-23
### Fixed
- disable transfer list sending when incoming cost type is not set for a product [Jira #FL-9206](https://futurelog.atlassian.net/browse/FL-9206)
- allow todays delivery date selection for local suppliers in Shopping Cart [Jira #FL-10605](https://futurelog.atlassian.net/browse/FL-10605)

## [3.12.1] - 2024-11-22
### Added
- External comments to Shopping Cart [Jira #FL-9877](https://futurelog.atlassian.net/browse/FL-9877), [Jira #FL-9879](https://futurelog.atlassian.net/browse/FL-9879), [Jira #FL-9881](https://futurelog.atlassian.net/browse/FL-9881)
- External comments to Order Archive [Jira #FL-9883](https://futurelog.atlassian.net/browse/FL-9883)
### Changed
- Prevent Transfer List sending with empty target stock for items [Jira #FL-9206](https://futurelog.atlassian.net/browse/FL-9206)
### Fixed
- Cross-property CAPEX log records loading [Jira #FL-10525](https://futurelog.atlassian.net/browse/FL-10525)
- CAPEX division filter for universal approvers [Jira #FL-10527](https://futurelog.atlassian.net/browse/FL-10527)
- CAPEX permissions [Jira #FL-10528](https://futurelog.atlassian.net/browse/FL-10528)
- missing Invoice account assignment info UI [Jira #FL-9404](https://futurelog.atlassian.net/browse/FL-9404)
### Refactoring
- errors parsing in Incomplete Bookings [Jira #FL-9458](https://futurelog.atlassian.net/browse/FL-9458)

## [3.12.0] - 2024-10-30
### Changed
- Various Approval center changes for "universal approver" [Jira #FL-9916](https://futurelog.atlassian.net/browse/FL-9916)
- Budgets changed to Cost Types for Shopping Cart [Jira #FL-10229](https://futurelog.atlassian.net/browse/FL-10229)
- Budgets changed to Cost Types for Purchase requests [Jira #FL-10230](https://futurelog.atlassian.net/browse/FL-10230)
- Delivery Notes UI/UX update [Jira #FL-10063](https://futurelog.atlassian.net/browse/FL-10063)
### Fixed
- Amount value is recalculated incorrectly on Freight and Import cost page [Jira #FL-10526](https://futurelog.atlassian.net/browse/FL-10526)
### Refactored
- Icons and symbols used by app and UI library

## [3.11.1] - 2024-10-30
### Added
- Freight Costs on Receivings [Jira-8571](https://futurelog.atlassian.net/browse/FL-8571)
- New WSD license support [Jira #FL-9516](https://futurelog.atlassian.net/browse/FL-9516)
### Changed
- Budgets changed to Cost Types for Shopping Cart [Jira #FL-9729](https://futurelog.atlassian.net/browse/FL-9729), [Jira #FL-10229](https://futurelog.atlassian.net/browse/FL-10229)
- Budgets changed to Cost Types for Purchase requests [Jira #FL-9732](https://futurelog.atlassian.net/browse/FL-9732)
### Fixed
- Shopping Cart data refreshing [Jira #FL-9762](https://futurelog.atlassian.net/browse/FL-9762)
- Supplier name display in suppliers list [Jira #FL-9610](https://futurelog.atlassian.net/browse/FL-9610), [Jira #FL-9763](https://futurelog.atlassian.net/browse/FL-9763)
- Receiving order item expiration date limits [Jira #FL-10092](https://futurelog.atlassian.net/browse/FL-10092)
- Receiving taxes rounding [Jira #FL-9060](https://futurelog.atlassian.net/browse/FL-9060)

## [3.11.0] - 2024-10-17
### Added
- Discounts for Shopping Cart products [Jira #FL-9575](https://futurelog.atlassian.net/browse/FL-9575)
- Discounts for Shopping Cart orders overview [Jira #FL-9577](https://futurelog.atlassian.net/browse/FL-9577)
- Discounts for Purchase Request Products [Jira #FL-9579](https://futurelog.atlassian.net/browse/FL-9579)
- Discounts for Order Archive products [Jira #FL-9580](https://futurelog.atlassian.net/browse/FL-9580)
- Discounts for Orders sending to supplier [Jira #FL-9581](https://futurelog.atlassian.net/browse/FL-9581)
### Fixed
- Unknown receiving error [Jira #FL-9904](https://futurelog.atlassian.net/browse/FL-9904)
### Refactoring
- `CartOverviewCubit` renamed to `CartTotalsCubit`
- `CartState` refactored to a cubit (`CartOverviewCubit`) with removing unnecessary API calls
### Updated
- Firebase dependencies

## [3.10.1] - 2024-10-02
### Added
- Spanish and Portuguese language support [Jira #FL-4845](https://futurelog.atlassian.net/browse/FL-4845)
- tax breakdown for invoice details [Jira #FL-7945](https://futurelog.atlassian.net/browse/FL-7945)
- font for division currency amount on invoice details screen [Jira #FL-9397](https://futurelog.atlassian.net/browse/FL-9397)
### Fixed
- missing UI elements on invoice details screen [Jira #FL-9395](https://futurelog.atlassian.net/browse/FL-9395)
- invoice account assignment amounts in division currency [Jira #FL-9398](https://futurelog.atlassian.net/browse/FL-9398)
- 4-eyes principle does not work as expected if skip#0 = OFF [Jira #FL-7702](https://futurelog.atlassian.net/browse/FL-7702)
- CAPEX approval level display [Jira #FL-7767](https://futurelog.atlassian.net/browse/FL-7767)
- make changes to capex approvals to work correctly with different approval engines

## [3.10.0] - 2024-09-05
### Added
- Notification for unmerged Astore articles on Process receiving [Jira #FL-8404](https://futurelog.atlassian.net/browse/FL-8404)
- ThermoWorks (ETI) single sensor BLE thermometers support [Jira #FL-8645](https://futurelog.atlassian.net/browse/FL-8645):
  - BlueTherm® One LE
  - Thermapen® Blue
  - ThermaQ® Blue
  - RayTemp® Blue
  - TempTest® Blue
  - TempTest® 2 Blue
- budget tab in Shopping Cart [Jira #FL-8153](https://futurelog.atlassian.net/browse/FL-8153)
- Budgets in Purchase Requests [Jira #FL-8152](https://futurelog.atlassian.net/browse/FL-8152)
### Fixed
- SOH of an article is displayed without rounding[Jira #FL-5664](https://futurelog.atlassian.net/browse/FL-5664)
- Incorrect message for adding few related orders [Jira #FL-7771](https://futurelog.atlassian.net/browse/FL-7771)
- Digital Invoices round values on tax breakdown [Jira #FL-9029](https://futurelog.atlassian.net/browse/FL-9029)
- Provisional Booking view pdf button condition [Jira #FL-9017](https://futurelog.atlassian.net/browse/FL-9017)
- Amount gross is not separated from tax breakdown if simple tax is used [Jira #FL-9031](https://futurelog.atlassian.net/browse/FL-9031)
- Update error message for editing closed store [Jira #FL-7073](https://futurelog.atlassian.net/browse/FL-7073)
- Tax name is cropped if more that 15 chars on Digital Invoices [Jira #FL-9032](https://futurelog.atlassian.net/browse/FL-9032)
- Title of date was cut in 'Provisional booking' tab on Process Receiving [Jira #FL-9033](https://futurelog.atlassian.net/browse/FL-9033)
- Search by barcode does not work for downloaded order lists in offline mode [Jira #FL-9004](https://futurelog.atlassian.net/browse/FL-9004)
- Tag size for core articles [Jira #FL-8019](https://futurelog.atlassian.net/browse/FL-8019)
- Barcode scanning loop in Inventory list for some Android devices [Jira #FL-5445](https://futurelog.atlassian.net/browse/FL-5445)
- Receiving Order Item Category field should not be mandatory [Jira #FL-8995](https://futurelog.atlassian.net/browse/)
### Changed
- Attachments edit name dialog to bottom sheet [Jira #FL-8744](https://futurelog.atlassian.net/browse/FL-8744)
### Updated
- Flutter SDK 3.24.3
- Dart SDK 3.4.0
- app dependencies

## [3.9.0] - 2024-08-29
### Added
- Tax breakdown in summary section of receiving [FL-7943](https://futurelog.atlassian.net/browse/FL-7943)
- AStore Orders in Orders Archive [Jira #FL-8401](https://futurelog.atlassian.net/browse/FL-8401)
- Tax breakdown for invoices [Jira #FL-7944](https://futurelog.atlassian.net/browse/FL-7944)
- Budget selection in shopping cart [Jira #FL-8151](https://futurelog.atlassian.net/browse/FL-8151)
### Changed
- Order Archive UI/UX
- single Order from Order Archive UI/UX
- Invoice Approvals and Invoice Archive list IU/UX update [Jira #FL-8482](https://futurelog.atlassian.net/browse/FL-8482)
- single Invoice screen UI/UX update [Jira #FL-8521](https://futurelog.atlassian.net/browse/FL-8521)
### Updated
- Flutter SDK to 3.22.2
- app 3rd party dependencies
- Flutter SDK requirements for app packages
- app packages 3rd party dependencies
### Fixed
- CAPEX request approval conditions [Jira #FL-7981](https://futurelog.atlassian.net/browse/FL-7981)
- cost center breakdown screen title [Jira #FL-7989](https://futurelog.atlassian.net/browse/FL-7989)
- provisional booking in progress message [Jira #FL-8010](https://futurelog.atlassian.net/browse/FL-8010)
- provisional booking error messages [Jira #FL-8454](https://futurelog.atlassian.net/browse/FL-8454)
- Receiving deposit items deletion [Jira #FL-8038](https://futurelog.atlassian.net/browse/FL-8038)
### Refactoring
- make `BuildContext` required for `Dialogs`
- use `Navigator` instead of `getx` for routing everywhere except `BasicErrorsHandlingService`, `DeepLinksService` and `PushNotificationsService` that need to be refactored first
- use `AppErrorHandler` everywhere instead of deprecated `ErrorsHandlingService`
### Removed
- deprecated `ErrorsHandlingService`

## [3.8.11] - 2024-08-29
### Fixed
- Receiving order items parsing issue [Jira #FL-9222](https://futurelog.atlassian.net/browse/FL-9222)

## [3.8.10] - 2024-08-15
### Fixed
- Receiving Cost Type Breakdown display [Senrty #MOBILE-APP-2M4](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/29850/)
- Cart overview report loading [Sentry #MOBILE-APP-2FV](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/27439/)
- Receiving Order Item file addition [Sentry #MOBILE-APP-2MN](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/30071/)
- Potential login issue [Jira #FL-9021](https://futurelog.atlassian.net/browse/FL-9021)
- Secure storage issues on Android [Sentry #MOBILE-APP-2JR](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/29408/), [Sentry #MOBILE-APP-2JW](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/29414/)

## [3.8.9] - 2024-07-11
### Added
- Process Receiving in approval status indication [Jira #FL-7918](https://futurelog.atlassian.net/browse/FL-7918)
- Recipes tab for showing all recipes [Jira #FL-5679](https://futurelog.atlassian.net/browse/FL-5679)
- image picker to PhotoService
- Process Receiving tax breakdown [Jira #FL-7943](https://futurelog.atlassian.net/browse/FL-7943)
- Purchase Request item attachments [Jira #FL-7685](https://futurelog.atlassian.net/browse/FL-7685)
- Purchase Request item changes history [Jira #FL-7685](https://futurelog.atlassian.net/browse/FL-7685)
- Purchase Request item editing [Jira #FL-7770](https://futurelog.atlassian.net/browse/FL-7770)
### Changed
- file attachments buttons look and feel [Jira #FL-7919](https://futurelog.atlassian.net/browse/FL-7919)
- remove paper edges detection from file attachment views [Jira #FL-7927](https://futurelog.atlassian.net/browse/FL-7927)
- use PhotoService to attach Receiving Delivered Items images
- show 3 decimal digits for receiving item price [Jira #FL-7948](https://futurelog.atlassian.net/browse/FL-7948)
- target Android SDK API level to 34
- split Delivery items table on Process Receiving [Jira #FL-8139](https://futurelog.atlassian.net/browse/FL-8139)
### Fixed
- receiving Delivery Note field error state [Jira #FL-7916](https://futurelog.atlassian.net/browse/FL-7916)
- displaying of already added Deposit Items in warehouse lookup [Jira #FL-8034](https://futurelog.atlassian.net/browse/FL-8034)
- changing of price for Approved FTO Articles [Jira #FL-8114](https://futurelog.atlassian.net/browse/FL-8114)
### Refactoring
- improve photo service errors handling
- use `Invoice.Digital.getFileUrl` instead of `Invoice.Digital.getDocumentUrl` [Jira #FL-7987](https://futurelog.atlassian.net/browse/FL-7987)
- `InvoiceScanningFileModel` added to handle `Invoice.Digital.getFileUrl` results
- separate calls for digital invoice file URL and digital invoice approval document url

## [3.8.8] - 2024-07-18
### Fixed
- storage issue when migrating from old preferences [Jira #FL-8549](https://futurelog.atlassian.net/browse/FL-8549)

## [3.8.7] - 2024-07-18
### Fixed
- storage issue when migrating from old preferences [Jira #FL-8549](https://futurelog.atlassian.net/browse/FL-8549)


## [3.8.6] - 2024-05-14
### Added
- Provisional Booking details screen [Jira #FL-4858](https://futurelog.atlassian.net/browse/FL-4858)
- cross-property CAPEX info on Authorized CAPEX tab [Jira #FL-6690](https://futurelog.atlassian.net/browse/FL-6690)
- cross-property CAPEX info on Closed CAPEX tab [Jira #FL-6695](https://futurelog.atlassian.net/browse/FL-6695)
- core item tag for Order Lists [Jira #FL-6445](https://futurelog.atlassian.net/browse/FL-6445)
- core item tag for Article Details in Shopping Cart, Catalog and Order List [Jira #FL-6448](https://futurelog.atlassian.net/browse/FL-6448)
- core item tag for Purchase Request products [Jira #FL-6449](https://futurelog.atlassian.net/browse/FL-6449)
- core item tag for Shopping Cart products [Jira #FL-6453](https://futurelog.atlassian.net/browse/FL-6453)
- History of Partial Receiving screen [Jira #FL-4988](https://futurelog.atlassian.net/browse/FL-4988)
- user main division time to App Setting
- real-time time updates on settings page
- ServerDateTimeConverter to convert dates from server responses to UTC time
### Changed
- cost-center field requirement when creating new Receiving Order [Jira #FL-7607](https://futurelog.atlassian.net/browse/FL-7607)
- warehouse lookup screen UI/UX update [Jira #FL-6179](https://futurelog.atlassian.net/browse/FL-6179)
- auth code field error when sending wrong code  [Jira #FL-4667](https://futurelog.atlassian.net/browse/FL-4667)
### Fixed
- cross-property CAPEX currency [Jira #FL-7606](https://futurelog.atlassian.net/browse/FL-7606)
- respect user's main division time in HALAL certificate validity detection [JIRA #FL-4883](https://futurelog.atlassian.net/browse/FL-4883)
### Refactoring
- server time parsing and saving
- user models refactoring
- remove UI dependencies from cubits, blocs and services by converting Locale to String
- user preferences strict typing, default values and cache optimization
- common preferences strict typing, default values and cache optimization
- UserPreferencesService and CommonPreferences service as a services through service locator
- PreferencesHelper to PreferencesStorage

## [3.8.5] - 2024-05-27
### Added
- user comments for CAPEX approval trail screen [Jira #FL-6733](https://futurelog.atlassian.net/browse/FL-6733)
- initial PO number and PDF view for Receiving Related Orders [Jira #FL-4800](https://futurelog.atlassian.net/browse/FL-4800)
- Webshop Cost Center lookup screen
- value input delegates for text filters
- Simplified Chinese locale support [Jira #FL-5558](https://futurelog.atlassian.net/browse/FL-5558)
- Process Receiving Item Inventory Unit lookup screen
### Changed
- allow entering 2 decimals when setting discount for receiving items [Jira #FL-5508](https://futurelog.atlassian.net/browse/FL-5508)
- data precision update for Receiving Delivered Items [Jira #FL-5509](https://futurelog.atlassian.net/browse/FL-5509)
- additional delivery redesign [Jira #FL-2592](https://futurelog.atlassian.net/browse/FL-2592)
- receiving booking result redesign [Jira #2703](https://futurelog.atlassian.net/browse/FL-2703)
- create receivings screen redesign [Jira #5303](https://futurelog.atlassian.net/browse/FL-5303)
- Receiving Order Item screen UI/UX update [Jira #FL-2596](https://futurelog.atlassian.net/browse/FL-2596)
- Cost Center name filters in Process Receiving now has suggestions popup [Jira #FL-4193](https://futurelog.atlassian.net/browse/FL-4193)
- Receiving Add Delivery Item screen UI/UX update [Jira #FL-2595](https://futurelog.atlassian.net/browse/FL-2595)
- Request Receiving Approval screen UI/UX update [Jira #FL-2845](https://futurelog.atlassian.net/browse/FL-2845)
- allow two decimal digits for Receiving Order Item discount [Jira #FL-7663](https://futurelog.atlassian.net/browse/FL-7663)
- three-dots menu comment editing for Receiving Deposit Item [Jira #FL-7696](https://futurelog.atlassian.net/browse/FL-7696)
- don't allow empty values submit in Single Line Text Field Editing Bottom Sheet
- Approval Center Approver selection screen UI/UX update [Jira #FL-7402](https://futurelog.atlassian.net/browse/FL-7402)
### Fixed
- division label disappearing on cross-property CAPEX [Jira #FL-7377](https://futurelog.atlassian.net/browse/FL-7377)
- wrong error messages for calculator [Jira #FL-5508](https://futurelog.atlassian.net/browse/FL-5508)
- wrong level amount when approving CAPEX from another division [Jira #FL-7477](https://futurelog.atlassian.net/browse/FL-7477)
- cross-property invoice approvals approver selection [Jira #FL-7613](https://futurelog.atlassian.net/browse/FL-7613)
- wrong translations on Process Receiving related orders [Jira #FL-6978](https://futurelog.atlassian.net/browse/FL-6978)
- missing orderId on Receiving Deposit items screen [Jira #FL-7583](https://futurelog.atlassian.net/browse/FL-7583)
- excessive state emission on Receiving Deposit Items screen
- missing error message on Receiving Deposit Items screen [Jira #FL-7584](https://futurelog.atlassian.net/browse/FL-7584)
- delete receiving confirmation dialog [Jira #FL-5302](https://futurelog.atlassian.net/browse/FL-5302)
- Process Receiving Order Item details fields order [Jira #FL-7656](https://futurelog.atlassian.net/browse/FL-7656)
- missing dropdown icons for Process Receiving Order Item details fields [Jira #FL-7658](https://futurelog.atlassian.net/browse/FL-7658)
- wrong list of inventory units for Receiving Order Item [Jira #FL-7659](https://futurelog.atlassian.net/browse/FL-7659)
- wrong filed focus after Cost Center lookup in Process Receivings [Jira #FL-7679](https://futurelog.atlassian.net/browse/FL-7679)
- maximum length for Receiving Order Item comment and attachment name [Jira #FL-7700](https://futurelog.atlassian.net/browse/FL-7700)
- empty comment and attachment name for Receiving Order Item [Jira #FL-7697](https://futurelog.atlassian.net/browse/FL-7697)
- wrong message when updating article totals for Receiving Order Item [Jira #FL-7691](https://futurelog.atlassian.net/browse/FL-7691)
- fix attachment delete confirmation dialog for Receiving Order Item [Jira #FL-7698](https://futurelog.atlassian.net/browse/FL-7698)
- product offer selection fro Purchase Requests [Jira #FL-7352](https://futurelog.atlassian.net/browse/FL-7352)

## [3.8.4] - 2024-05-21
### Added
- core item icon to Catalog screen [Jira #FL-6447](https://futurelog.atlassian.net/browse/FL-6447)
### Fixed
- Current order PDF issue [Jira #FL-6975](https://futurelog.atlassian.net/browse/FL-6975)
- IMS store details issue [Jira #FL-6982](https://futurelog.atlassian.net/browse/FL-6982)
- approver visibility conditions for CAPEX declining process [Jira #FL-7148](https://futurelog.atlassian.net/browse/FL-7148)
- attachment file groups order [Jira #FL-4439](https://futurelog.atlassian.net/browse/FL-4439)
- CAPEX approval trail empty screen [Jira #FL-7216](https://futurelog.atlassian.net/browse/FL-7216)
- old file name shown when renaming attachment file [Jira #FL-7197](https://futurelog.atlassian.net/browse/FL-7197)
- wrong report loading for closed inventory [Jira #FL-5796](https://futurelog.atlassian.net/browse/FL-5796)
- pdf and images viewing with special symbols in name [Jira #FL-4662](https://futurelog.atlassian.net/browse/FL-4662)
- cross-property CAPEX approvers list [Jira #FL-7429](https://futurelog.atlassian.net/browse/FL-7429)
### Changed
- delivery note data redesign [Jira #2588](https://futurelog.atlassian.net/browse/FL-2588)
- break down by cost center redesign [Jira #2593](https://futurelog.atlassian.net/browse/FL-2593)
- last approver comment display for CAPEX [Jira #FL-7222](https://futurelog.atlassian.net/browse/FL-7222)
- show total in two currencies in Order archive when order is not in division currency [Jira #FL-2940](https://futurelog.atlassian.net/browse/FL-2940)

## [3.8.3] - 2024-05-05
### Added
- CAPEX activity log screen [Jira #FL-6838](https://futurelog.atlassian.net/browse/FL-6838)
- CAPEX approval trail screen [Jira #FL-6839](https://futurelog.atlassian.net/browse/FL-6839)
- cross-property CAPEX approvals [Jira #FL-6685](https://futurelog.atlassian.net/browse/FL-6685)
### Fixed
- cross-property CAPEX messages [Jira #FL-7012](https://futurelog.atlassian.net/browse/FL-7012)
- approval request cached files issue [Jira #FL-6631](https://futurelog.atlassian.net/browse/FL-6631)
- CAPEX filtering issue [Jira #FL-7150](https://futurelog.atlassian.net/browse/FL-7150)

## [3.8.2] - 2024-04-30
### Fixed
- CAPEX filtering issue [Jira #FL-7150](https://futurelog.atlassian.net/browse/FL-7150)

## [3.8.1] - 2024-04-23
### Added
- single store master data screen [Jira 5575](https://futurelog.atlassian.net/browse/FL-5575)
### Changed
- CAPEX approval requests screen separated in three tabs with UI/UX update: Active, Authorized and Closed [Jira #FL-4225](https://futurelog.atlassian.net/browse/FL-4225), [Jira #FL-4226](https://futurelog.atlassian.net/browse/FL-4226), [Jira #FL-4227](https://futurelog.atlassian.net/browse/FL-4227)
- CAPEX details screen UI/UX update [Jira #FL-4229](https://futurelog.atlassian.net/browse/FL-4229)
- CAPEX approval flow UI/UX update [Jira #FL-4230](https://futurelog.atlassian.net/browse/FL-4230), [Jira #FL-4346](https://futurelog.atlassian.net/browse/FL-4346)
- Receiving Related Orders screens UI/UX update [Jira 2590](https://futurelog.atlassian.net/browse/FL-2590)
- services filters added for General TS05 BLE thermometer
### Updated
- flutter_reactive_ble to 5.3.1

## [3.8.0] - 2024-04-02
### Added
- scanned invoices: pdf file picker [Jira #3923](https://futurelog.atlassian.net/browse/FL-3923)
- MS Windows compatibility [Jira #FL-3991](https://futurelog.atlassian.net/browse/FL-3991)
- image compression for scanned documents with phone camera [Jira #FL-4928](https://futurelog.atlassian.net/browse/FL-4928)
- page size in app bar fro scanned document page screen
### Changed
- barcode scanned disabled for Windows
- camera usage disabled for Windows
- cached network image usage removed for Windows
- app icon badges service disabled for Windows
- Devices menu item hidden for Windows
- Help Center menu item hidden for Windows
- scanned documents page editing disabled for Windows
- PDF preview disabled on Windows
- STT quantity editing option disabled for Windows
### Fixed
- scanned documents page editing visual issue
- process receivings search behavior [Jira #FL-6249](https://futurelog.atlassian.net/browse/FL-6249)
### Removed
- filters by user name and order requested by under the non-po receivings tab [Jira #5300](https://futurelog.atlassian.net/browse/FL-5300)
### Refactoring
- photo edge detection is a service now

## [3.7.4] - 2024-03-27
### Changed
- remove service filter when searching for Generate TS05 thermometer [Jira #FL-6271](https://futurelog.atlassian.net/browse/FL-6271)

## [3.7.3] - 2024-03-22
### Fixed
- Improve Generate TS05 thermometer discoverability and errors reporting

## [3.7.2] - 2024-03-05
### Added
- ability to rename the uploading file before attaching it to Approved PRs [Jira #4483](https://futurelog.atlassian.net/browse/FL-4483)
- filter "Status" to non-po receivings [Jira #5298](https://futurelog.atlassian.net/browse/FL-5298)
- General ToolSmart TS05 IR thermometer support [Jira #FL-4880](https://futurelog.atlassian.net/browse/FL-4880)
- Provisional Bookings list in Process Receivings [Jira #FL-4659](https://futurelog.atlassian.net/browse/FL-4659)
- store master data screen [Jira #FL-5293](https://futurelog.atlassian.net/browse/FL-5293)
- filter "Delivery Date" to orders [Jira #5620](https://futurelog.atlassian.net/browse/FL-5620)
### Changed
- rename “Date range” to “Order date”[Jira #5278](https://futurelog.atlassian.net/browse/FL-5278)
### Fixed
- no data loaded when the screen is swiped for switching tabs on Receiving Details [Jira #FL-5807](https://futurelog.atlassian.net/browse/FL-5807)
- no possibility to set zero discount on Details tab for Receiving [Jira #FL-FL-5808](https://futurelog.atlassian.net/browse/FL-5808)
- saved Bluetooth device disconnecting right after connect
- app restart when resizing window on Windows 11
- Testo 104-IR BT thermometer issues [Jira #FL-4473](https://futurelog.atlassian.net/browse/FL-4473)
- doc attachments: user can't attach files with original name to Approved PRs [#Jira-5864](https://futurelog.atlassian.net/browse/FL-5864)

## [3.7.1] - 2024-02-27
### Added
- "My Groups' Purchase Requests" status filter for Purchase Requests [Jira #FL-5375](https://futurelog.atlassian.net/browse/FL-5375)
### Updated
- UI/UX of the Receiving Details Screen [Jira #FL-2583](https://futurelog.atlassian.net/browse/FL-2583)
- UI/UX of the Receiving Order Items [Jira #FL-2589](https://futurelog.atlassian.net/browse/FL-2589)
- 2FA screen, increase height of FLTextField [Jira #FL-5295](https://futurelog.atlassian.net/browse/FL-5295)
### Fixed
- bug in receiving product expiration date selector [Jira FL-4741](https://futurelog.atlassian.net/browse/FL-4741)
- tags overflow on purchase request card [Jira #FL-4740](https://futurelog.atlassian.net/browse/FL-4740)
- bug in supplier list: there is no favorite icon next to supplier with soon to expire docs only [Jira #FL-5265](https://futurelog.atlassian.net/browse/FL-5265)
- misconfigured white labeling [Jira #FL-5340](https://futurelog.atlassian.net/browse/FL-5340)

## [3.7.0] - 2024-02-07
### Added
- error message to Doc attachments [Jira #FL-4442](https://futurelog.atlassian.net/browse/FL-4442)
- halal label for offline Shopping Cart and Order Lists [Jira #FL-4029](https://futurelog.atlassian.net/browse/FL-4029)
### Updated
- UI/UX of the Process Receiving [Jira #FL-2570](https://futurelog.atlassian.net/browse/FL-2570)
- UI/UX of contract management screens [Jira #FL-3977](https://futurelog.atlassian.net/browse/FL-3977)
- UI/UX of suppliers overview screen [Jira #FL-3975](https://futurelog.atlassian.net/browse/FL-3975)
- local translations
- Flutter SDK and dependencies
### Fixed
- proper handling of receiving order item without quantity [Jira #FL-5210](https://futurelog.atlassian.net/browse/FL-5210)

## [3.6.5] - 2024-01-22
### Added
- booking to Provisional Bookings for receiving [Jira #FL-4839](https://futurelog.atlassian.net/browse/FL-4839)

## [3.6.4] - 2024-01-17
### Added
- `melos` package to simplify the analyze/lint processes of the app and local packages
- local copy of `flutter_bluetooth_serial` package with fixes for Android 12+
- measurement taking bottom sheet [Jira #FL-4561](https://futurelog.atlassian.net/browse/FL-4561)
- HTML rendering to the announcement cards [Jira #FL-4563](https://futurelog.atlassian.net/browse/FL-4563)
- cross-property invoice approval [Jira #FL-4566](https://futurelog.atlassian.net/browse/FL-4566)
- certificate halal filter on the Contracts page [Jira #FL-4878](https://futurelog.atlassian.net/browse/FL-4878)
### Updated
- UI/UX of the bluetooth devices module [Jira #FL-4561](https://futurelog.atlassian.net/browse/FL-4561)
- UI/UX of the settings screen [Jira #FL-4752](https://futurelog.atlassian.net/browse/FL-4752)
- UI/UX of the licenses screen [Jira #FL-4752](https://futurelog.atlassian.net/browse/FL-4752)
- UI/UX of the imprint screen [Jira #FL-4752](https://futurelog.atlassian.net/browse/FL-4752)
- UI/UX of the announcements screen [Jira #FL-4724](https://futurelog.atlassian.net/browse/FL-4724)
- UI/UX of the two-factor auth screen [Jira #FL-3403](https://futurelog.atlassian.net/browse/FL-3403)
### Fixed
- error in the `FLSnackBar` happening on the not redesigned screens [Sentry #21620](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/21620)
- inventory search by 2 symbols [Jira #FL-3459](https://futurelog.atlassian.net/browse/FL-3459)
### Removed
- stop words removal functionality as it caused faulty search results
- config checking for Power Bi [Jira #FL-4891](https://futurelog.atlassian.net/browse/FL-4891)

## [3.6.3] - 2023-12-26
### Added
- support of the 'Open drawer on long press' feature to the `DynamicAppBarV3`
- `translator` extension on the `BuildContext`
- halal certificate viewing fo Supplier contract management [Jira #FL-3862](https://futurelog.atlassian.net/browse/FL-3862)
- halal labels for products in Process Receiving [Jira #FL-3863](https://futurelog.atlassian.net/browse/FL-3863)
- halal labels for products in Shopping Cart [Jira #FL-3864](https://futurelog.atlassian.net/browse/FL-3864)
- halal labels for products in Purchase Requests [Jira #FL-3866](https://futurelog.atlassian.net/browse/FL-3866)
- halal labels for products on Ordering [Jira #FL-3869](https://futurelog.atlassian.net/browse/FL-3869), [Jira #FL-3871](https://futurelog.atlassian.net/browse/FL-3871)
- halal certificate filtering on Catalog search [Jira #FL-3872](https://futurelog.atlassian.net/browse/FL-3872)
- halal certificate badge and info for Catalog products [Jira #FL-3865](https://futurelog.atlassian.net/browse/FL-3865)
- logging of the measurements in the unknown formats from the Testo 104-IR BT thermometer [Jira #FL-4473](https://futurelog.atlassian.net/browse/FL-4473)
### Updated
- image view screen now has a share button to send files to other apps
- connection process to the Testo 104-IR BT thermometer to be able to skip key activation when it is not needed [Jira #FL-4473](https://futurelog.atlassian.net/browse/FL-4473)
### Fixed
- infinite loading dialog on scanned documents screen after file picker cancellation
- consistency of the `NavigationMenuDrawer` UI, now it uses the old theme no matter the context
- external files remove after uploading scanned invoice
- stock field editing for receiving deposit item [Jira #FL-4110](https://futurelog.atlassian.net/browse/FL-4110)
- empty booking send [Jira #FL-4189](https://futurelog.atlassian.net/browse/FL-4189)
- inter-property direct booking [Jira #FL-4033](https://futurelog.atlassian.net/browse/FL-4033)
### Changed
- main action buttons from `FLTextButton` to `FLOutlinedButton` in all occurrences of the `FLDialog` [Jira #FL-4589](https://futurelog.atlassian.net/browse/FL-4589)
### Removed
- filtering by service when looking up for Testo 104-IR BT thermometer [Jira #FL-4473](https://futurelog.atlassian.net/browse/FL-4473)

## [3.6.2] - 2023-12-04
### Added
- documents attachments for Shopping Cart and Purchase request [Jira #FL-3655](https://futurelog.atlassian.net/browse/FL-3655)
- documents attachments for Process Receiving [Jira #FL-3980](https://futurelog.atlassian.net/browse/FL-3980)
- documents attachments for Process Invoicing [Jira #FL-3521](https://futurelog.atlassian.net/browse/FL-3521)
### Fixed
- external files remove after uploading scanned invoice
- stock field editing for receiving deposit item [Jira #FL-4110](https://futurelog.atlassian.net/browse/FL-4110)
- empty booking send [Jira #FL-4189](https://futurelog.atlassian.net/browse/FL-4189)
- inter-property direct booking [Jira #FL-4033](https://futurelog.atlassian.net/browse/FL-4033)

## [3.6.1] - 2023-11-20
### Added
- uploaded invoice statuses [Jira #FL-2622](https://futurelog.atlassian.net/browse/FL-2622)
- new inter-property direct booking support upon booking creation [Jira #FL-3883](https://futurelog.atlassian.net/browse/FL-3883)
- inter-property direct booking items adding [Jira #FL-3884](https://futurelog.atlassian.net/browse/FL-3884)
- inter-property direct booking items editing/deletion [Jira #FL-3885](https://futurelog.atlassian.net/browse/FL-3885)
- "STK IP Transfer" transfer type filter for bookings [Jira #FL-3893](https://futurelog.atlassian.net/browse/FL-3893)
- "Cost Center - Order Requested By" filter added for receiving [Jira #FL-3880](https://futurelog.atlassian.net/browse/FL-3880)
- account assignment division in invoice details for cross-charge enabled divisions [Jira #FL-3964](https://futurelog.atlassian.net/browse/FL-3964)
- reference to the partial receiving [Jira #FL-3882](https://futurelog.atlassian.net/browse/FL-3882)
### Changed
- hide supplier documents information when contract management is disabled for division [Jira #FL-3439](https://futurelog.atlassian.net/browse/FL-3439)
### Updated
- scanned documents redesign
- Flutter and dependencies

## [3.6.0] - 2023-11-15
### Added
- truck temperature per article [Jira-3502](https://futurelog.atlassian.net/browse/FL-3502)
- integration for AEG ABKS1 BLE Kitchen Scale [Jira #FL-3827](https://futurelog.atlassian.net/browse/FL-3827)
- `action` property to the `ErrorNoResults` widget
- ability to restart scanning on Bluetooth device lookup screen after no devices are found
- ability to restart scanning on Bluetooth device lookup screen after the error happened
- ability to specify services when scanning for BLE devices
- Free Text Orders for local Suppliers [Jira #FL-3794](https://futurelog.atlassian.net/browse/FL-3794)
### Fixed
- jumping measurement unit and original value on measurement taking screen
### Updated
- `flutter_reactive_ble` package
### Testing
- test covered `AegABKS1ScaleBleAdapter`
- test covered `BluetoothDeviceBleAdapter`
- test covered `AegABKS1ScaleBleSetupCubit`
- test covered `AegABKS1ScaleBleSetupState`
- test covered `BluetoothLowEnergyLookupCubit`
- test covered `MeasurementTakingCubit`
- test covered `AegABKS1ScaleMeasurementFactory`
- test covered `BluetoothDeviceType`
- test covered `SavedDeviceModel`
### Refactoring
- app theme management

## [3.5.6] - 2023-11-7
### Added
- second amount in division currency for invoices when supplier currency is different from division currency [Jira #FL-3174](https://futurelog.atlassian.net/browse/FL-3174)
### Changed
- show "view PDF" action for receiving only when it has an order pdf [Jira #FL-3013](https://futurelog.atlassian.net/browse/FL-3013)
- Recipes icon [Jira #FL-3637](https://futurelog.atlassian.net/browse/FL-3637)
- recipes enabled for everyone [Jira #FL-3618](https://futurelog.atlassian.net/browse/FL-3618)
- recipes permission update [Jira #FL-3696](https://futurelog.atlassian.net/browse/FL-3696)
### Fixed
- initial push notification handling [Jira #FL-3029]https://futurelog.atlassian.net/browse/FL-3029
- invoice amount and currency display [Jira #FL-3443](https://futurelog.atlassian.net/browse/FL-3443)
- recipe expenses cost of sales display fix [Jira #FL-3453](https://futurelog.atlassian.net/browse/FL-3453)
- consolidated cart items editing conditions [Jira #FL-3528](https://futurelog.atlassian.net/browse/FL-3528)
### Refactored
- depositItemsCubit [Jira #FL-2713](https://futurelog.atlassian.net/browse/FL-2713)
- pass language parameters in ReceivingsRepository from cubits [Jira #FL-2717](https://futurelog.atlassian.net/browse/FL-2717)
- receiving cubit [Jira #FL-2711](https://futurelog.atlassian.net/browse/FL-2711)
- move filter cubit to shared cubits [Jira #FL-2714](https://futurelog.atlassian.net/browse/FL-2714)
### Testing
- DepositItemsCubit [Jira #FL-2713](https://futurelog.atlassian.net/browse/FL-2713)

## [3.5.5] - 2023-10-03
### Added
- follow new user permission to make receiving items prices editable [Jira #FL-2655](https://futurelog.atlassian.net/browse/FL-2655)
- new filters for Delivery Notes [Jira #FL-2967](https://futurelog.atlassian.net/browse/FL-2967)
- use default catalog sorting from division configuration in catalog [Jira #FL-2417](https://futurelog.atlassian.net/browse/FL-2417)
- invoice comment to invoice lists in Approvals and Invoice Archive [Jira #FL-2797](https://futurelog.atlassian.net/browse/FL-2797)
### Changed
- don't allow to change product quantity if it was consolidated [Jira #FL-2072](https://futurelog.atlassian.net/browse/FL-2072)
- allow negative quantity for iter-property transfer bookings [Jira #FL-2942](https://futurelog.atlassian.net/browse/FL-2942)
- quantity validation error improvements [Jira #FL-3183](https://futurelog.atlassian.net/browse/FL-3183)
### Fixed
- lists filter and sorting when changed during loading [Jira #FL-3340](https://futurelog.atlassian.net/browse/FL-3340)
### Refactoring
- SessionPermissions model to be freezed

## [3.5.4] - 2023-09-20
### Added
- basic universal links support [Jira #FL-2183](https://futurelog.atlassian.net/browse/FL-2183)
- handling supplier contract links from emails [Jira #FL-2185](https://futurelog.atlassian.net/browse/FL-2185)
- supplier contract status filter [Jira #FL-2186](https://futurelog.atlassian.net/browse/FL-2186)
- possibility to use QR code with product stock id while in “continuous scanning” [Jira #FL-2054](https://futurelog.atlassian.net/browse/FL-2054)
- supplier name filter for purchase requests [Jira #FL-2137](https://futurelog.atlassian.net/browse/FL-2137)
- item name on quantity change dialog in inventory [Jira #FL-1079](https://futurelog.atlassian.net/browse/FL-1079)
- item highlight after quantity update in inventory [Jira #FL-1079](https://futurelog.atlassian.net/browse/FL-1079)
- shopping cart: Filter "Require approval", "Ready to send", "All" [Jira #FL-2074] (https://futurelog.atlassian.net/browse/FL-2074)
- product comment in purchase request view [Jira #FL-2463](https://futurelog.atlassian.net/browse/FL-2463)
### Changed
- don't allow to change Free Text Order product category when product is already in cart [Jira #FL-2109](https://futurelog.atlassian.net/browse/FL-2109)
- don't allow to set PR product quantity beyond minimum order quantity [Jira #FL-2126](https://futurelog.atlassian.net/browse/FL-2126)
- extend product comment length to 1000 symbols [Jira #FL-2677](https://futurelog.atlassian.net/browse/FL-2677)
- improve sentry events logging
### Fixed
- store mapping for inter-property transfer lists [Jira #FL-2674](https://futurelog.atlassian.net/browse/FL-2674)
- inter-property transfer lists negative item quantity
- Purchase requests section permission [Jira #FL-2146](https://futurelog.atlassian.net/browse/FL-2146)
- store and cost type lookup for transfer list when store mapping enabled for division [Jira #FL-2036](https://futurelog.atlassian.net/browse/FL-2036), [Jira #FL-2037](https://futurelog.atlassian.net/browse/FL-2037)
- push notification redirect handling when app was closed [Jira #FL-2340](https://futurelog.atlassian.net/browse/FL-2340)
- fix approver selection for Purchase Requests [Jira #FL-3042](https://futurelog.atlassian.net/browse/FL-3042)
### Testing
- orders overview, products tests

## [3.5.3] - 2023-09-12
### Fixed
- Some Purchase Requests was not sent to supplier after approval [Jira #FL-2732](https://futurelog.atlassian.net/browse/FL-2732)
### Removed
- app setting for enabling and disabling auto send orders

## [3.5.2] - 2023-08-04
### Added
- Sentry events for direct PR sending

## [3.5.1] - 2023-07-11
### Added
- default value for the `..._enabled` properties in `DivisionModel` [Sentry #16435](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16435)
- `fl.HALAL_CERTIFICATION` translation label
- URL launcher
- open unsupported for viewing documents in approval requests in an external app [Jira #FL-1960](https://futurelog.atlassian.net/browse/FL-1960)
### Fixed
- null check error in next approver selectors [Sentry #15930](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15930)
- `RangeError` in `TransferListItemsCubit` [Sentry #16071](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16071)
- orders overview and orders to send loading when supplier is no longer available in division [Sentry #16178](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16178)
- possibility to launch multiple scanning processed [Sentry #16350](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16350)
- refresh indicator color for the high-luminance primary colors
- supplier contract days to expiration count [Jira #FL-2048](https://futurelog.atlassian.net/browse/FL-2048)
- `RangeError` in `InventoryListCubit` [Sentry #15888](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15888)
- an ability to tap through barcode scanner view [Jira #FL-2101](https://futurelog.atlassian.net/browse/FL-2101)
- expired contract badge when expiration date is today [Jira #FL-2075](https://futurelog.atlassian.net/browse/FL-2075)
- Impossible to remove comments in shopping cart
### Updated
- speed of the success sign on the inventory process, to be less distracting
- color of the success sign on the inventory process to be the same as the primary division color
- flutter to v3.10.5
- 3rd party dependencies
### Removed
- offline builder
### Refactoring
- replaced deprecated OfflineBuilder with DisabledBuilder
- images loading optimization to decrease network and memory usage
- optimized code generation by disabling the generation of unused code
- utils separation
### Testing
- free text order product lookup tests
- free text order supplier lookup tests
- note tests

## [3.5.0] - 2023-07-04
### Added
- cart auto consolidate functionality [Jira #FL-1766](https://futurelog.atlassian.net/browse/FL-1766)
- expired contracts information in suppliers list [Jira #FL-639](https://futurelog.atlassian.net/browse/FL-639)
- expired documents screen [Jira #FL-106](https://futurelog.atlassian.net/browse/FL-1935)
- expiring documents dashboard item [Jira #FL-641](https://futurelog.atlassian.net/browse/FL-641)
- free text orders creation in cart
- free text order product editing
- free text order indication for cart products, cart orders, receiving orders, receiving order items, purchase requests
### Changed
- fractional value allowed for units in square meters [Jira #FL-2032](https://futurelog.atlassian.net/browse/FL-2032)
### Fixed
- supplier contracts search criteria [Jira #FL-2050](https://futurelog.atlassian.net/browse/FL-2050)
- target store and cost type lookup results for in-house transfer list items [Jira #FL-2037](https://futurelog.atlassian.net/browse/FL-2037), [Jira #FL-2036](https://futurelog.atlassian.net/browse/FL-2036)
### Testing
- added free text order creation/editing screen tests
- expired documents screen tests

## [3.4.12] - 2023-06-27
### Added
- ability to view attached images on the purchase request documents tab [Jira #1623](https://futurelog.atlassian.net/browse/FL-1623)
- new purchase requests status filter values: "Closed approved" and "Closed declined" [Jira #FL-1824](https://futurelog.atlassian.net/browse/FL-1824)
- new purchase requests filters: "Cost center name", "Item description" and "Category" [Jira #FL-1899](https://futurelog.atlassian.net/browse/FL-1899)
- item link from supplier in catalog products [Jira #FL-1762](https://futurelog.atlassian.net/browse/FL-1762)
- permission check for automatic transfer lists loading on sync [Sentry #15912](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15912)
- `RepaintBoundary` to the `ExpandableBadge` to reduce repaints
- `RepaintBoundary` to the card where `ExpandableBadge` is used to reduce repaints
- null check error in `CartProductCardPriceAndAction` [Sentry #16087](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16087)
- null check error in `CartOrderToApproveCard` [Sentry #16071](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16071)
- cross-division Purchase Requests approval [Jira #FL-1715](https://futurelog.atlassian.net/browse/FL-1715)
- approver name to the last log message for invoices from Invoices Archive [Jira #FL-1971](https://futurelog.atlassian.net/browse/FL-1971)
### Changed
- inventory unit lookup to packing unit lookup on free text order item screen
- contracts information display for supplier details [Jira #FL-639](https://futurelog.atlassian.net/browse/FL-639)
### Fixed
- purchase request documents loading after the `url` format changed [Jira #1623](https://futurelog.atlassian.net/browse/FL-1623), [Sentry #16037](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16037)
- possibility of `BadState: No Element` error in `Dialogs.closeOpenDialog` [Sentry #16088](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/16088)
- error when catalog item external link was empty [Jira #FL-1762](https://futurelog.atlassian.net/browse/FL-1762)
### Removed
- visibility_detector package
- Deprecated JCenter artifact repository
### Testing
- added receiving related orders cubit tests
- added approval request documents widgets tests
### Updated
- flutter to v3.10.3
- 3rd party dependencies
### Removed
- dart_code_metrics package

## [3.4.11] - 2023-06-23
### Fixed
- error caused by the PDF share button not being disabled after tap [Sentry #15881](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15881)

## [3.4.10] - 2023-06-15
### Added
- AppErrorHandler as a new way of errors handling
- translations for EXT and TMP catalog item types
- injection of the stack traces on the repositories level for better error handling and analysis
- `imageUrl` support to the `CartProductModel`
- waiting for the `Catalog.rebuild` method response
- filter by order id for receiving orders
- `make generate-tr-labels` and `make generate-env` commands to speed up the generation of the translation labels and environment variables
- route names for the dialogs, so now it is easier to analyze navigation breadcrumbs in the Sentry
- arguments to the routes, so now it is easier to analyze navigation breadcrumbs in the Sentry
- `orderCostCenterId` passing when getting order and order's products [Jira #FL-1886](https://futurelog.atlassian.net/browse/FL-1886)
- `orderCostCenterId` to the `PurchaseRequestProductModel` to be able to open order from purchase request product card [Jira #FL-1886](https://futurelog.atlassian.net/browse/FL-1886)
### Fixed
- 'no translation' label appearance on the order list product's details when a name is not available 
- bug that was causing the `api.common.not_found` error after booking item deletion
- UI bug in a bottom bar on the booking items screen
- app bar on transfer list order screen, now search and 'View PDF' buttons won't be show if booking was deleted to prevent `api.common.not_found` error
- bug in checking for visibility of request customer id button on the catalog product card, which was the cause of `api.supplier.customer_id_can_not_be_requested` error [Sentry #15720](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15720)
- `ProviderNotFoundException` on continuous scanning in inventory [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/72696c93e2a246179520f1f59a4a7811)
- `ProviderNotFoundException` on approval request update [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/04cbc06098a24c5fb754782b59e98a12)
- the ability to close the dialog with the back button on Android
- wrong image url in locally saved order list and cart products
- order list selection by integration of `OrderLists.Item.editableListLookup` method
- null exception on `PasswordRecoveryScreen` [Sentry #15803](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15803)
- stale context usage in the translator [Sentry #15856](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15856)
- common not found error when getting order from another cost center [Sentry #15799](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/15799)
- booked receiving status screen [Jira #FL-1705](https://futurelog.atlassian.net/browse/FL-1705)
### Updated
- flutter to v3.10.2
- 3rd party dependencies
- `launch.json` to disable Impeller rendering engine due to rendering bugs
### Removed
- FakeApiService and FakeApiResponse as they are redundant
### Refactoring
- receiving available orders screen
- password recovery functionality
- replaced `ErrorsHandlingService` with `AppErrorHandler` in `PagedEntityList` and `PagelessEntityList`
- invoices to use new API methods instead of deprecated ones [Jira #FL-1772](https://futurelog.atlassian.net/browse/FL-1772)
- receiving related orders cubit, screen
### Testing
- added tests for receiving available orders cubit


## [3.4.9] - 2023-05-23
### Added
- cost center id to the search service's products search request
- product `type` to the `/media/offer` urls
- `RECIPE` scope for the `Warehouse.itemLookup` [Jira #FL-1433](https://futurelog.atlassian.net/browse/FL-1433)
- time stamp on the receiving item measurement card
- `Ready to book` filter on bookings manage screen [Jira #FL-948](https://futurelog.atlassian.net/browse/FL-948)
- restriction for receiving item's split delivery quantity to be in range [0 - 9999999999] [Jira #FL-1431](https://futurelog.atlassian.net/browse/FL-1431)
### Fixed
- bug with null check operator on the order list products screen [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/20df1f966817438287db1b0ce8795e99)
- use supplierId instead of supplierName to search catalog products for specific supplier
- some logos cropping issue
- permission check for delivery notes access
- permission check for recipe sharing
- no element exception on warehouse lookup screen [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/9840278492c6407da106b82667318dbe)
### Refactoring
- order list products cubit
### Changed
- deprecated `Orders` methods on alternative `Order` ones
- inventory unit lookup to packing unit lookup on ims product mapping screen
- inventory unit lookup to packing unit lookup on receiving item details screen
- local signature package to the fixed one from pub.dev
- `inventoryUnit` property of the `TransferListOrderItemModel` to be nullable [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/11207fe6e5ac4f84b65f863f6497bd38)
### Removed
- negative values allowance for receiving item's split delivery quantity
### Updated
- flutter to v3.7.12
- 3rd party dependencies

## [3.4.8] - 2023-03-16
### Changed
- allow delivery date selection in cart orders for a year ahead
### Fixed
- document scanner language to use phone language on iOS
- document scanner language to use app language on Android
### Refactoring
- remove UI dependencies from cubits
- app licenses screen to use cubit
- create cubit for invoice approval trail screen
### Testing
- cubit tests for catalog product offer screen
### Updated
- Flutter requirements updated to 3.7.7

## [3.4.7] - 2023-03-07
### Added
- "update" button on outdated version dialog and screen to redirect user to an app store
- currency to transfer list items [Jira #FL-1135](https://futurelog.atlassian.net/browse/FL-1135)
- "Open order" action for finished purchase request products
### Changed
- use correct currency from API for transfer list items, transfer list orders, booking products and booking approval requests
### Fixed
- screen layout name could now be also tapped to change layout in setting
- show "no translation" for products without names when changing order list products order
- pdf report URLs format
- don't show "view pdf" option for booking approval request without voucher
- product name display in catalog product offers
### Updated
- update invoice approval screen cubit tests
- Flutter 3.7.6 and dependency update
### Refactoring
- booking approval requests screens for better network state handling and widget testing
- update invoice approval screen for better network state handling and widget testing
- capex approval request screen for better network state handling and widget testing
- invoice approval request screen for better network state handling and widget testing
- receiving approval request screen for better network state handling and widget testing
- order again screen for better network state handling and widget testing
- receiving delivery details screen for better network state handling
- booking approval requests screens refactoring
- approval request document model refactoring
### Testing
- cubit tests added for every approval request screen
- cubit tests improved for approval trail tabs
- cubit tests improved for approval request document tabs
- cubit tests improved for approval request messages tabs
- model tests added for approval request screen

## [3.4.6] - 2023-01-30
### Added
- default sorting for transfer lists search
- target division price information for inter-property booking approval request products
- `listId` parameter in the most InterProperty API methods
### Changed
- transfer list card look and feel
- booking approval product card look and feel
- a deletion info message on a deleted product in purchase request approval
- max order list read note size from 255 to 1000
### Updated
- flutter to v3.7.0
- 3rd party dependencies

## [3.4.5] - 2023-01-20
### Added
- supplier contract management functionality
- delivery date indication on purchase request product card [Jira #FL-970](https://futurelog.atlassian.net/browse/FL-970)
- division and cost center ids on const center selection screen [Jira #FL-979](https://futurelog.atlassian.net/browse/FL-979)
- source stock qty and price indication for inter-property transfer list products [Jira #FL-974](https://futurelog.atlassian.net/browse/FL-974)
- missing error messages translations
### Changed
- default sorting option for the catalog search to be name asc and price asc
- imageUrl of the catalog product to use url returned by the API
### Fixed
- document scanning issue [Sentry #14006](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/14006/)
- issue with purchase request item name translation [Jira #FL-995](https://futurelog.atlassian.net/browse/FL-995)
- links opening from supplier details screen
### Updated
- 3rd party dependencies
- static i18n files
### Testing
- added cubit tests for supplier contract management
- added cubit tests for recipe share functionality
- added cubit tests for recipe details screen
- added cubit tests for recipe ingredients ordering functionality
- updated cubit tests for recipe ingredients screen

## [3.4.4] - 2023-01-06
### Added
- recipe notes functionality
- favorite recipes functionality
- dev flavor
- badge on the bottom of the screen to indicate beta or dev flavor
### Fixed
- calculator rounding issue [Jira #FL-904](https://futurelog.atlassian.net/browse/FL-904)
- listType property of the TransferListOrderModel to handle the null case [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/0f9923a844034d1b99a25e0c7a67eca1)
### Updated
- 3rd party dependencies

## [3.4.3] - 2022-12-28
### Added
- redirect to the orders sending result screen after sending purchase request to the supplier [Jira #FL-820](https://futurelog.atlassian.net/browse/FL-820)
- approver type (group or user) indication
- approval groups support
### Changed
- the default API call timeout to 60 seconds
### Fixed
- currencies format in the receivings module
- issue with catalog search filters reset [Jira #FL-877](https://futurelog.atlassian.net/browse/FL-877)
### Updated
- flutter to v3.3.10
- 3rd party dependencies

## [3.4.2] - 2022-12-15
### Added
- transfer list deletion functionality
- filterByDivision parameter to the CostCenter.searchForWebshopCostCenter
- booking errors indication
- booking result screen
### Fixed
- null exception on receiving details screen when costCenter is null
### Updated
- flutter to v3.3.9
- 3rd party dependencies

## [3.4.1] - 2022-12-09
### Added
- support of the dynamic decimal places configuration for currencies [Jira #FL-645](https://futurelog.atlassian.net/browse/FL-645)
- thumbnail pictures for recipes categories and recipes tiles
- source and target division names on booking item details screen
- recipe order functionality
- recipe pdf view and printing functionality
- share recipe functionality
- inter-property type indication on booking card [Jira #FL-703](https://futurelog.atlassian.net/browse/FL-703)
### Changed
- endpoint for the products' images to load offer-specific images [Jira #FL-542](https://futurelog.atlassian.net/browse/FL-542)
- replaced product ID to position ID in offer image URLs
### Refactoring
- removed OfferProductModel, now we have one ProductOfferModel
### Fixed
- wrong permission check for loading the invoice information on the delivery note screen
- null check operator used on a null value exception when loading in-house lists on sync [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/78068061a79041e194511515072933db)
- negative quantity allowance for inter-property booking items [Jira #FL-702](https://futurelog.atlassian.net/browse/FL-702)
- check for the availability of the inter-property transfers in the division
### Updated
- 3rd party dependencies

## [3.4.0] - 2022-12-01
### Added
- create transfer list screen
- transfer list cost center lookup screen
- inter property list source division lookup screen
- inter property list item to add lookup screen
- inter property list item add screen
- inter property list item mapping screen
- inter property list item merge screen
- inter property list product update/add screen
- inter property list item remap screen
- possibility to add an item if inter property sync between divisions is enabled
- constraints for inter property order bookings
- catalog indexing details screen
- transfer list order pdf view functionality
- indexing details info on catalog search result screen when search index is not ready
- recipe ingredients screen with the ability to add them to cart
- recipe preparation steps screen
- recipe allergens and additives screen
- recipe nutrition value screen
- recipe costs screen
- turkish language support [Jira #FL-237](https://futurelog.atlassian.net/browse/FL-237)
- content units per order unit value on catalog product card [Jira #FL-579](https://futurelog.atlassian.net/browse/FL-579)
- minimal order quantity and surcharge amount indication on the cart order overview card [Jira #FL-578](https://futurelog.atlassian.net/browse/FL-578)
- separate package fl_lints to store all project lint rules
### Changed
- condition to always allow float quantity for transfer list's orders [Jira #FL-524](https://futurelog.atlassian.net/browse/FL-524)
### Fixed
- wrong capex approvals permission check [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/1f080782b8b34b7b9ddd85d1374cdc6b), [Sentry #9400](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/9400/events/f5b2931bcb5444378f71d828a4ee9e81)
- currency for receiving order total [Jira #FL-511](https://futurelog.atlassian.net/browse/FL-511)
- bug when only one item was added to the receiving, even if multiple were selected
- LateInitializationError in order list's products list [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/7caa9e345ff54004b230a8b1005605b2)
- null check operator used on a null value exception [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/6dcd096a102f4b8ab740d58b0c335678), [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/06725108ae1940cbb14dbf66edddb39a)
- possible visual text overflow on CAPEX approval request card
- wrong CAPEX approval request invoice's id type [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/c95d5a90d1e94400b1a52dd7bd2cd670)
- navigation parameters passing bug on the OTP screen
- currency for receiving order total
- catalog search errors handling
### Updated
- recipes module UI
- flutter to v3.3.8
- 3rd party dependencies
- static i18n files
### Removed
- unused receiving add item screen (we use receiving add items screen instead)
### Testing
- added widget tests for inventory eans edit functionality
- added cubit and widget tests for transfer list creation functionality
- added cubit and widget tests for transfer list order placement functionality
- added cubit tests for booking approval requesting functionality
- added cubit and widget tests for transfer list cost center lookup functionality
- added cubit and widget tests for inter property list source division lookup functionality
- added cubit tests for inter property list item to add lookup functionality
- added cubit tests for catalog search functionality
- added cubit tests for catalog indexing details functionality
- added cubit tests for recipe costs view functionality
- updated cubit tests for recipe categories search functionality
- updated cubit tests for recipe ingredients view and order functionality
- added cubit tests for recipe nutrients view functionality
- added cubit tests for recipe preparations steps view functionality
- updated cubit tests for recipes search functionality
- added cubit tests for single entity cubit
### Refactoring
- improved catalog search performance and stability

## [3.3.25] - 2022-10-24
### Fixed
- bug in ApprovalRequestCubit (state was updated, but not emitted)
- bug with delivery date selection [Jira #FL-395](https://futurelog.atlassian.net/browse/FL-395)
- booking details update screen fields availability
- catalog search behavior on slow network connection
### Updated
- flutter to v3.3.5
- 3rd party dependencies
### Refactoring
- action buttons refactoring on: approver selection screen, inhouse list screens, item lookup screens, booking manage screens, login screens, receivings screens, order lists screens, scanned documents, shopping cart

## [3.3.24] - 2022-10-13
### Added
- inventory force unlock functionality
- labels for product details in catalog, shopping cart and order lists 
- inventory list difference report view
- ability to change orientation to landscape on pdf view screens
### Fixed
- order of disable conditions in booking product's confirmation button
- snackbar message after successful booking
### Testing
- added cubit tests for force unlock functionality
- added widget tests for inventory lists confirmation functionality

## [3.3.23] - 2022-10-04
### Changed
- circular border removed on pencil icon buttons
- cart product delete icon replaced with swipe gesture
- declined booking badge icon from x to thumb down
### Added
- capex push notifications support
- food labels filter in catalog
- declined item info for purchase request item
- order list pdf overview
### Updated
- 3rd party dependencies
### Refactoring
- inventory module
### Testing
- added cubit tests for inventory module
- added password recovery cubit tests
- added password reset cubit tests

## [3.3.22] - 2022-09-29
### Added
- in-house lists tests
- capex approval API integration and tests
- capex approval screens
- in-house lists tests
- capex tests
- adjustments for the declined bookings [Jira #FL-248](https://futurelog.atlassian.net/browse/FL-248)
- can_be_reset field to the BookingApprovalRequestModel
- haptic feedback for switches
### Fixed
- handling of the Firebase errors [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/19925df5c47a47c5a329afa5d3a84cb4)
- error caused by the possibility of adding a product with zero qty into the cart [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/71a08be4877446129e41c65aecc5cc5d) 
- speech to text localeId handling [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/0fec494efae64512b0b901de0aeadbd4)
### Updated
- flutter to v3.3.3
- 3rd party dependencies

## [3.3.21] - 2022-09-15
### Added
- in-house booking details tests
- in-house booking product tests
- in-house booking reason lookup tests
- booking manage tests
- in-house order products tests
- create booking tests
### Refactored
- booking manage screen
- booking reason lookup screen
- in-house order products screen
- in-house transfer lists screen
### Changed
- costCenterId in the inventory list model to be nullable [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/584ccb98f5d647b49e3f43747c9495e5)
### Fixed
- wrong dialog labels in inhouse order approval request resetting dialog
- bug in cart orders sending when an empty response appears
- bug with error handling when the system cannot create indexer file [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/170df7a9ad0042bfacf92ee95aaec9ea)
- null check error on purchase request screen [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/c33eeadd4955451fb97e84a98bb6092f)
- not safe response handling in the search service [Sentry #12193](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/12193/events/9803c234087244e6b6802d921b7d40b6)
### Updated
- flutter to v3.3.2
- 3rd party dependencies
- minimum iOS version to 11
### Removed
- firebase_crashlytics package

## [3.3.20] - 2022-09-06
### Added
- inhouse list product tests
- update inhouse list product details functionality
- update inhouse list product details tests
- permission check for sending invoice to accounting
- functionality to toggle all subsequent deliveries in receiving
- receiving subsequent delivery tests
- pdf view tests
- supplier change functionality tests
- order target cost centers functionality tests
- coverage setting in gitlab-ci.yml
- additional information in In-house orders: log messages, approval process, approver
- ability to request In-house order approval
- ability to reset In-house order approval
- in-house booking items tests
- in-house orders tests
### Removed
- request approval button on the invoice screen, when auto approve is available
- all driver tests as they are entirely outdated
### Changed
- Power BI report layout to mobile_portrait
### Refactored
- Booking items screen
### Fixed
- bug on pdf screen when HTTP response code was not 200 and screen was empty
- bug on send order after approval cost centers config screen
- offline cart products uploading
- supplier details screen
### Updated
- 3rd party dependencies

## [3.3.19] - 2022-08-19
### Added
- send order after approval cost centers config screen
- messages on cards about auto sending order after approval
- inhouse list add item functionality
- inhouse list add item tests
- inhouse list delete functionality
- additional useful targets in Makefile
- inhouse list items re-ordering screen
- inhouse list items re-ordering tests
### Refactored
- inhouse list deprecated field replaced with actual one
- inhouse list products screen and cubit
- flavours to be consistent for both platforms
- firebase initialization process
### Updated
- auto send order after approval switch to be enabled by default
- 3rd party dependencies
### Fixed
- platform specific warnings
- bug with empty in-house list products names
- bug on request booking approval screen when no approvers found
- recipe card icons
- bug with catalog access permission
- bug in reset theme tile

## [3.3.18] - 2022-08-11
### Added
- recipe categories screen
- recipes screen
- send to accounting button on invoice approval request screen
- text filters on invoice approvals search
- feature flags support
- help center tests
- ims products mapping tests
- cubit and screen tests for set pin functionality
- cubit tests for cost center lookup functionality
- recipes tests
### Refactoring
- scanner module (navigation, cubits, structure)
- receivings navigation
- scanned documents navigation
- warehouse lookup navigation
### Updated
- 3rd party dependencies

## [3.3.17] - 2022-07-25
### Added
- send order after approval functionality
- shared order list download
- recipes api integration
- open main navigation menu by long tap on back button in header
### Updated
- timeout of the Cart.sendOrders method to 600 seconds
- 3rd party dependencies
### Fixed
- bug in approver lookup for the initial receiving approval request
### Refactoring
- shared screens navigation
- catalog navigation
- suppliers module navigation
- shopping cart navigation
- approvals navigation
- bluetooth devices navigation
- ims and reports navigation

## [3.3.16] - 2022-07-21
### Added
- possibility to update delivery note total on the main screen of the receiving
- possibility to update discount for all order items in receiving at once
- receiving approval requests tests
- tr_labels package for translation labels generation to use them safely
- add to cart progress and result screen when ordering again from orders archive
- IMS items mapping screen
- approval center tests
### Refactoring
- i18n functionality
### Updated
- permissions checks in order lists
- flutter to v3.0.5
- 3rd party dependencies
- local cart tests
### Fixed
- order again for more then 20 items
- receiving confirmation result handling
- View PDF button functionality for receiving in approval
- bulk price offers add to cart functionality
- message dialog scroll bug

## [3.3.15] - 2022-07-05
### Added
- local cart upload results screen
- local cart upload progress dialog
- local cart products tests
- orders counter in shopping cart
- order status indication after sending
- badge on the order archive card to indicate that the order was not sent
- subsequent delivery items presence indication on receiving screen
- permission check for the inventory module
- theme update after division switch
### Changed
- shopping cart action buttons' visibility to depend on the presence of the orders
- envify plugin to envy
### Updated
- local cart products uploading (now unloading occurs in batches of 20 products)
- flutter to v3.0.3
- 3rd party dependencies
### Fixed
- bug with PDF files caching
- bug in offline inventory search [Sentry #11151](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/11151)
### Removed
- unused translation labels

## [3.3.14] - 2022-06-22
### Added
- text filters in receiving screens
- text filters in delivery notes
- approval request messages tests
- booking approval request products tests
- approval request logs tests
- receiving approval request products tests
- purchase request product offers tests
### Updated
- booking approval requests refactoring
- calculator tests
### Fixed
- calculator rounding for all values
- offline translations endpoint
- push notifications doubling after relogin
- next approver label on cards, when approver name is null

## [3.3.13] - 2022-06-17
### Added
- clear message when no order lists found in the cost center
- offline inhouse lists
### Fixed
- offline order lists progress counter
- prints during the tests runs
- bug in receiving approval request document deletion
### Removed
- flare package dependency (replaced with rive)
### Updated
- "Delivery note is invoice" switch visibility in receiving details according to permissions
- flutter to v3.0.2
- 3rd party dependencies

## [3.3.12] - 2022-06-09
### Added
- offline order lists
- offline cart products
- switch to enable automatic offline order lists update during app startup
- new filter options in delivery notes
- approver selection tests
- speech recognition option in calculator in receivings
- ability to share pdf file from pdf view
- booking creation screen
- request receiving approval screen
- multiple operations in calculator
- button to access offline inventories
- check whether or not it is possible to add an item to the inventory list
### Fixed
- toggle display on delivery note card
### Updated
- 3rd party dependencies
- static i18n files
- check connection status only while on foreground
- calculator refactoring

## [3.3.11] - 2022-05-30
### Added
- receiving approvals
- calculator text highlight
- calculator tests
### Fixed
- infinity value check in calculator
- regression with quantity rounding during inventory taking (in inventory, decimal quantity is possible for all measurement units)
### Updated
- 3rd party dependencies
- deprecated OrderLists API methods replaced with new ones

## [3.3.10] - 2022-05-16
### Added
- search by barcode in warehouse item lookup
- search by barcode in receiving product to add lookup
### Updated
- calculator input
- 3rd party dependencies
### Fixed
- possible exception when displaying messages for some purchase request approvals
- division redirect from push notification

## [3.3.9] - 2022-05-10
### Added
- archived status filter for bookings manage
- date range filter for bookings manage
- booking voucher view in booking manage
- recipe transfer for booking details
- delivery note availability toggle
- delivery note warnings display
- delivery note invoice viewing
### Changed
- receiving button visibility for delivery note depending on edit availability
- suppliers list navigation menu item moved to ordering section
- PDF view
### Fixed
- device orientation change on iOS
### Updated
- flutter to v3.0.0
- 3rd party dependencies

## [3.3.8] - 2022-05-03
### Fixed
- minor issues
### Updated
- 3rd party dependencies

## [3.3.7] - 2022-04-28
### Fixed
- product name in in-inhouse order item was empty (no translation)

## [3.3.6] - 2022-04-28
### Added
- calc keyboard for product quantity and prices in inventory and receivings
### Fixed
- disabled text color for some buttons with light themes

## [3.3.5] - 2022-04-20
### Added
- booking voucher view for booked inventory lists

## [3.3.4] - 2022-04-18
### Added
- approve menu item on inventory list screen
- unlock menu item on inventory list screen
- category filter for reports
- 'booked' and 'signed' inventory list status filters
- support for the light theme colors
### Changed
- item not found message during inventory list product scanning
### Updated
- 3rd party dependencies

## [3.3.3] - 2022-04-13
### Added
- ability to select text information in various places
- no pages message for scanned document
- order list creation screen
### Changed
- floating action button default colors
- delete product button in bookings replaced with menu item
- 'Open catalog' button hidden for empty read-only order lists
### Fixed
- shopping cart permission checks

## [3.3.2] - 2022-04-12
### Added
- landscape mode for help center
- ability to select text information in various places
### Fixed
- inventory item edit button for tablets
- inventory continuous scanning mode in case of no network

## [3.3.1] - 2022-04-10
### Added
- help center
### Fixed
- transparent navigation bar bug on document scan in iOS 15
- symbols check on set new password screen

## [3.3.0] - 2022-04-10
### Added
- offer_price to the product quantity in cart update bottom sheet
- additional filters to the bookings manage screen
- receiving process start warning dialog on order card

## [3.2.12] - 2022-04-08
### Added
- description on the Power BI report card
### Updated
- deposit item details screen
### Updated
- 3rd party dependencies

## [3.2.11] - 2022-04-07
### Added
- delete receiving functionality
- deposit item details screen
### Fixed
- empty cart note handling
- cart note translations

## [3.2.10] - 2022-04-05
### Added
- goods receivings module in main navigation menu
- separate screens for receivings and orders
- landscape orientation support to the Power BI report view screen
- go to login button on the password reset success screen
- price last updated at date to the order list and product offer screens
- create new receiving screen
### Updated
- delivery note card
### Fixed
- inventory speech to text settings and permission requests
- screen width for some lists on tablets

## [3.2.9] - 2022-04-04
### Added
- shopping cart note tab

## [3.2.8] - 2022-04-01
### Changed
- default state of speech-to-text for inventory
### Fixed
- removing product from better price screen in shopping cart
- sorting option highlight in catalog

## [3.2.7] - 2022-04-01
### Added
- Power BI reports
### Changed
- password reset screen
- two factor authentication errors display
### Updated
- 3rd party dependencies

## [3.2.6] - 2022-03-31
### Added
- two factor authentication
### Changed
- password recovery screen
### Updated
- flutter to v2.10.4

## [3.2.5] - 2022-03-23 
### Added
- image upload functionality to the receiving item screen
- separate screen to block access to the app in case of version is outdated
- error indicators to the receiving and receiving item screens
- validation and description to the receiving item discount field 
### Updated
- 3rd party dependencies

## [3.2.4] - 2022-03-14
### Added
- testo thermometer company and key retrieval from the API
- counter on the connected devices navigation drawer tile
### Updated
- 3rd party dependencies

## [3.2.3] - 2022-03-09
### Fixed
- wrong unit on temperature measuring
### Updated
- static i18n files

## [3.2.2] - 2022-03-08
### Updated
- digi 166 scale setup flow to be more clear
- flutter to v2.10.3
- 3rd party dependencies

## [3.2.1] - 2022-03-03
### Updated
- mettler toledo bc scale setup flow to be more clear
### Added
- coreItem filter in products search

## [3.2.0] - 2022-03-01
### Added
- bluetooth devices module
- measurement taking functionality in the receiving item details
### Updated
- 3rd party dependencies

## [3.1.13] - 2022-02-28
### Added
- intake list inventory report on the inventory list screen
- redirect button to the catalog if order list products search results are empty
- separate screen to show available price levels for the product
### Updated
- read note card to be available even if order list products search results are empty
- price levels availability indication
- flutter to v2.10.2
### Fixed
- bug with wrong order lists loading in order list selection screen

## [3.1.12] - 2022-02-10
### Added
- not submit to supplier checkbox on the order card on the send order screen
### Updated
- flutter to v2.10.0
- 3rd party dependencies

## [3.1.11] - 2022-02-08
### Changed
- order list products order value replaced with current product quantity in cart
### Added
- current product price to quantity change dialog
- price levels availability icon (percent icon) near product price where available
- price levels display to quantity change dialog
### Fixed
- price currency display in quantity change dialog
- wrong errors handling when changing product quantity in cart, order lists, catalog
- cart product total price not updating after quantity change

## [3.1.10] - 2022-02-04
### Updated
- Result class to be more type safety and useful

## [3.1.9] - 2022-02-01
### Changed
- Order lists refactored to use PagedEntityListCubit
### Added
- OrderListsCubit tests

## [3.1.8] - 2022-01-26
### Changed
- New Pin screen refactored with flutter bloc
- Purchase Requests Approval refactored with flutter bloc
- Invoices refactored with flutter bloc
- Booking approval requests refactored with flutter bloc
### Added
- NewPinCubit
- PurchaseRequestsCubit
- InvoicesCubit
- Shared PagedEntityListCubit
- Shared PagedEntityList widget
- BookingApprovalRequestsCubit
- InvoicesCubit tests
- PurchaseRequestsCubit tests
- BookingApprovalRequestsCubit tests
### Removed
- NewPinBloc
- PurchaseRequestsBloc
- InvoicesBloc
- BookingApprovalRequestsBloc

## [3.1.7] - 2022-01-24
### Fixed
- receiving product discount selector to accept zero value
- receiving product split delivery store and cost type lookups to depend on selected cost center
### Updated
- build.yaml file to speedup code generation
- 3rd party dependencies

## [3.1.6] - 2022-01-21
### Added
- Budget warning and budget info screens
- Dashboard item for percent value display

## [3.1.5] - 2022-01-18
### Fixed
- button layout error in invoice approve screen
### Added
- animated transitions to the text editing screen

## [3.1.4] - 2022-01-12
### Fixed
- Fix stream events sending to closed stream sink on app initialization
### Added
- Success login flow integration test
- Keys for navigation drawer items for automated testing
- Navigation drawer testing robot
- Catalog testing robot
- Announcements screen testing robot
- Login testing robot
- Pin testing robot
- Integration test flow:
  - Logout
  - Login
  - Go to Catalog
  - Find first product that is not yet in cart
  - Add it to cart
  - Go to Cart
  - Check the product being added  

## [3.1.3] - 2022-01-06
### Added
- notifications cubit test
- pin confirmation cubit test
- text edit cubit test

## [3.1.2] - 2022-01-05
### Added
- dashboard cubit test
- multiple products adding to the receiving
- offer percent and offer end date to the catalog product card and details screen
### Fixed
- pin code field layout issue

## [3.1.1] - 2022-01-04
### Fixed
- Bug in SignatureConfirmationCubit
- Bug in BookingItemsCubit
- Bug with deletion in bookings manage list
### Changed
- location of the local packages to the separate `packages` folder

## [3.1.0] - 2021-12-30
### Removed
- unused code
### Updated
- flutter to 2.8.1 version
- 3rd party dependencies
### Changed
- AnnouncementsBloc to AnnouncementsCubit
### Added
- tests for the AnnouncementsCubit
### Fixed
- dialog issue on the InventoryControlScreen

## [3.0.116] - 2021-12-28
### Changed
- Pin confirmation screen refactored to use Flutter Bloc
- Scanned documents refactored to use Flutter Bloc
- Receiving orders refactored to use Flutter Bloc
- Signature confirmation refactored to use Flutter Bloc
- Request booking approval refactored to use Flutter Bloc
- Inhouse Orders refactored to use Flutter Bloc
- Booking items refactored to use Flutter Bloc
### Added
- PinConfirmationCubit
- ScannedDocumentCubit
- ScannedDocumentsCubit
- OrdersCubit in receivings_module
- SignatureConfirmationCubit
- RequestBookingApprovalCubit
- InHouseOrdersCubit
- BookingItemsCubit
### Removed
- PinConfirmationBloc
- ScannedDocumentBloc
- ScannedDocumentsBloc
- OrdersBloc from receivings_module
- SignatureConfirmationBloc
- RequestBookingApprovalBloc
- InHouseOrdersBloc
- BookingItemsBloc

## [3.0.115] - 2021-12-20
### Added
- Approval trail tab to the invoice approval request screen
### Removed
- Some unused code

## [3.0.114] - 2021-12-17
### Added
- Fixes to the code to meet lint rules
### Updated
- 3rd party dependencies

## [3.0.113] - 2021-12-16
### Added
- Announcements API methods integration
- Comment to the purchase request card and details screen
- New dashboard card to support long messages
### Updated
- Removed comment field on approval request stage in booking and purchase requests screens

## [3.0.112] - 2021-12-15
### Added
- New cost centers search screen
- CostCenter API methods integration
### Updated
- Move approval log records to the separate screen
### Fixed
- Auto redirect in announcements screen

## [3.0.111] - 2021-12-10
### Added
- Receiving booking result screen
- Delivery notes screen
- Delivery note screen
- Delivery note pdf view
### Updated
- Move target cost center button in the receiving from three dots menu to the delivery section
- UI of the target cost center card
- Flutter to 2.8.0 version

## [3.0.110] - 2021-12-09
### Added
- Cost center information to subsequent delivery in Goods Receiving
- "View pdf" button for orders in Goods receiving list
### Changed
- Round edit buttons with pencil icon changed to text buttons for tablets
- App bar action button replaced with a button in the bottom of the screen in order list products reordering
- App bar action button replaced with a button in the bottom of the screen on inhouse booking product screen
- App bar action button replaced with a button in the bottom of the screen on sending inventory lists screen
### Fixed
- Target cost center card style
- Product nutrients display in Catalog

## [3.0.109] - 2021-12-09
### Added
- Approval trail tab for Booking Approval Request and Purchase Request

## [3.0.108] - 2021-12-09
### Fixed
- Cost type, store, quantity update error, when cost center is not selected
### Changed
- All confirmation and message dialogs to use `Dialogs` helper

## [3.0.107] - 2021-12-08
### Added
- Target cost centers view for Orders archive and Goods receiving
### Changed
- User without the permission to send orders will not be allowed to reset purchase requests

## [3.0.106] - 2021-12-07
### Added
- Receiving additional orders screen
- Receiving subsequent delivery screen
- Receiving confirmation button

## [3.0.105] - 2021-12-07
### Added
- Product stock information in purchase requests
- Quantity change button will remain disabled for the products in purchase request where quantity change is not allowed
### Changed
- All confirmation dialogs to use `Dialogs.showConfirmationDialog`
- If user can't send orders, he will not be allowed to delete approved products from the shopping cart
- If user can't send orders, he will not be allowed to move purchase request to the shopping cart
- Permissions to manage Goods receiving
### Fixed
- Handle `Cart.getProductTotals` new response format (min capability level: 20211207)
- Handle `Cart.getProductTotals` "not found" response

## [3.0.104] - 2021-12-06
### Added
- Deposit items screen
- SOH, minStock, maxStock values to the product card of the booking approval request
- Missing Receivings API methods
### Updated
- 3rd party dependencies

## [3.0.103] - 2021-12-03
### Added
- `printing` library for PDF view and printing
- Supplier conditions for product offer screen in Catalog
- Setting reliance when showing confirmation dialogs
- Product totals info in cart when changing product quantity
- Product totals info in purchase request when changing product quantity
### Changed
- barcode_scan plugin refactored to support Android v2 embedding
- url_launcher plugin updated
- Every MultilineTextDialog replaced with TextEditScreen
### Fixed
- Text color in booking approvals and purchase requests
- Phone calls and url launching on Supplier screen
### Removed
- `pdf_flutter` library (replaced with `printing`)
- `flutter_pdf_printing` library (replaced with `printing`)

## [3.0.102] - 2021-11-30
### Changed
- Forms width constrained for wide screens
- Action buttons width constrained for wide screens
- Tablet UI updated for Approval center screens
- Quantity edit icon button replaced with text button for tablets
- Content width constrained for some screens
- Dashboard UI updated fro wide screens

## [3.0.101] - 2021-11-26
### Updated
- 3rd party dependencies

## [3.0.100] - 2021-11-25
### Fixed
- Inventory screen state restore after network disconnection
- Inventory list offline handling
- Order List offline behavior
- Offline list refresh for purchase request and booking approval products
- Disable quantity change in Booking manage while offline
- Booking reason selection behavior while offline
- Warehouse lookup behavior while offline
- Fix inventory confirmation Skip button display
### Added
- Product Offer offline handling in Catalog
- "See orders to approve" button to redirect user from Orders to Submit to Orders to Approve in Cart is there are no orders to submit
- Offline handling by app drawer
### Changed
- flutter_bloc library update to 8.0.0
- Allow select approver when offline
### Refactoring
- Blocs refactored to remove deprecated `mapEventToState` method

## [3.0.99] - 2021-11-24
### Added
- Offline handling for remaining screens
### Removed
- Cost center ID from Bookings.update API request

## [3.0.98] - 2021-11-19
### Fixed
- Bug with wrong cost center id on booking confirm

## [3.0.97] - 2021-11-19
### Added
- Offline mode UI to the products search screen
- Offline mode UI to the product details screen
- Offline mode UI to the order lists screen
- Offline mode UI to the order list screen
- Offline mode UI to the order list products reorder screen
- Offline mode UI to the order list supplier change screen
- Offline mode UI to the order list selection screen
- Offline mode UI to the orders screen
- Offline mode UI to the order screen
- Offline mode UI to the order again screen
- Offline mode UI to the send message to supplier screen
- Offline mode UI to the suppliers screen
- Offline mode UI to the supplier details screen
- Offline mode UI to the request supplier id screen
- Offline mode UI to the cost center lookup screen
- Offline mode UI to the dashboard screen
- Offline mode UI to the warehouse lookup screen
- Offline mode UI to the pdf view screen
- Offline mode UI to the shopping cart overview screen
- Offline mode UI to the shopping cart orders to send screen
- Offline mode UI to the shopping cart orders to approve screen
- Offline mode UI to the shopping cart product screen
- Offline mode UI to the shopping cart product offers screen
- Offline mode UI to the shopping cart order sending result screen
- Offline mode UI to the inventory control screen
- Offline mode UI to the inventory signature confirmation
- Offline mode UI to the inventory lists confirm screen

## [3.0.96] - 2021-11-17
### Added
- New quantity bottom sheet with more flexible settings and without previous bugs
- Ability to set negative values in receivings module in places where it is available
### Fixed
- Wrong offline mode behavior in the inventory quantity bottom sheet

## [3.0.95] - 2021-11-16
### Fixed
- Bug with wrong index in LazyLoadingListView when leading is presented [Sentry #9145](https://sentry.flogintra.ch/organizations/futurelog-ag/issues/9145/?project=4&query=is%3Aunresolved)

## [3.0.94] - 2021-11-15
### Added
- Offline mode UI to the order lists screen
- Offline mode UI to the order list details screen
### Updated
- 3rd party dependencies
### Fix
- Expandable badge overlap issues
- Bug with supplier details render overflow

## [3.0.93] - 2021-11-10
### Added
- Refresh indicator to the receiving orders screen
- Totals bottom bar in receiving items screen
### Fixed
- Bug in receiving delivery note editing
### Removed
- Offline mode screen in receiving details

## [3.0.92] - 2021-11-10
### Changed
- Catalog search results will not cleared while offline
- Search query in catalog entered while offline will be applied once online
- Add "itemType" field into Catalog.getOfferById API request

## [3.0.91] - 2021-11-10
### Added
- Language selector in speech to text
- Digitize functionality in speech to text, to convert text into numbers

## [3.0.90] - 2021-11-10
### Changed
- Shared Add To Cart Button will stay disabled while offline
- Shared Quantity Bottom Sheet action button will remain disabled while offline

## [3.0.89] - 2021-11-10
### Added
- Basic receivings functionality

## [3.0.88] - 2021-11-04
### Added
- Order list read note integration
- Order list product comment integration
- Open pdf and reset buttons onto the invoice card
- Separate open pdf button in the invoice details
### Updated
- 3rd party dependencies
### Fix
- Bug in BookingModel

## [3.0.87] - 2021-11-03
### Added
- OrderLists.replaceItem API method to change order list item supplier
- New push notifications UI 

## [3.0.86] - 2021-11-02
### Added
- Supplier OCI catalog integration with the redirect to the cart

## [3.0.85] - 2021-11-02
### Changed
- Cart products list UI changes for Tablets
- Catalog UI changes for tablets
- Order lists UI changes for tablets
- Inventory lists UI changes for tablets
- Quantity bottom sheet UI changes for tablets
- Dashboard UI changes for tablets
- Add to cart button UI changes for tablets across the app
- Filter chips UI changes for tablets across the app

## [3.0.84] - 2021-11-01
### Added
- Bookings.Product.add method integration
- Products grouping by the positionId and itemType in order again process to sum their quantity
- Cart.addOrUpdateItem API method
- Cart.addOrUpdateManyItems API method
- New lint rules
- Supplier OCI catalog redirect
### Updated
- Renamed OrganizationUnitModel to DivisionModel
- 3rd party dependencies
### Removed
- OrgUnits table from the local database
### Fixed
- Some todos

## [3.0.83] - 2021-10-22
### Added
- Division name, order approvals flag, organization unit license and currency code are now stored in user preferences 
### Changed
- UserModel refactored to freezed
- User.getByToken API method replaced with User.getActiveUser
- User.login API method replaced with User.logon
- OrganizationUnitModel refactored to freezed
- Division.getInfo API method replaced with Division.getOneById
- Store necessary organization unit information in preferences instead of DB
### Removed
- Deprecated catalog methods: Catalog.getAll, Catalog.search
- Deprecated Orders.getAll
- Deprecated inventory methods: Inventory.lock, Inventory.log, Inventory.unlock, Inventory.update, Inventory.getAll
- OrganizationUnitModel as service

## [3.0.82] - 2021-10-19
### Added
- Category filter to the inventory list screen
- Swipe to refresh in cart overview [#42123](https://redmine.711media.de/issues/42123)
- Cart report (PDF) [#41548](https://redmine.711media.de/issues/41548)
- Sorting filter to the inventory list screen
### Updated
- Flutter to 2.5.3 version

## [3.0.81] - 2021-10-19
### Added
- Product offer end date in Catalog
### Changed
- 11 and more filter values in one filter across the app will be collapsed to take less space
### Fixed
- Don't allow to change product quantity in cart if the product is already approved in cart
- Update product details after changing quantity on cart product details screen
### Changed
- Error handling improvements in Catalog search
- Actual price color on Order Again Screen
- Nutrition, Allergens and Additives display on product offer screen in Catalog
- Bottom information on product offer screen in Catalog changed according to design
- Cost center and orderer filters position changed on Orders screen

## [3.0.80] - 2021-10-18
### Fixed
- InHouse list screen bugs
- InHouse list transfer request screen bugs
- ExpandableBadge overflow issue [#41947](https://redmine.711media.de/issues/41947)
- Bottom bar button now appear on top of the keyboard [#41869](https://redmine.711media.de/issues/41869)
### Changed
- AppBar action icon buttons to the elevated BottomBar buttons [#41848](https://redmine.711media.de/issues/41848)
### Removed
- Waiting of the Catalog.rebuild API method result [#41946](https://redmine.711media.de/issues/41946)
### Updated
- 3rd party dependencies

## [3.0.79] - 2021-10-13
### Added
- InHouseLists.searchOrders API method
- Warehouse.itemLookup API method
- Bookings.update and Bookings.confirm API methods
### Fixed
- Invoice approval workflow

## [3.0.78] - 2021-10-12
### Fixed
- Cart button functionality on Cart Product Screen
### Added
- User permission check when requesting supplier customer ID
- Orders filtering by orderer
- Orders filtering by cost center
- Request Customer ID button on Product Offer in Catalog
- Request Customer ID button on Product in Order List

## [3.0.77] - 2021-10-11
### Fixed
- Wrong delivered qty in booking overview product card
### Added
- Message about incomplete booking item details
- Dynamic dashboard items
- DynamicAppBar widget to automatically handle trailing icon and action
### Updated
- 3rd party dependencies

## [3.0.76] - 2021-10-08
### Added
- Bookings management dependent lookups [#41100](https://redmine.711media.de/issues/41100) 

## [3.0.75] - 2021-10-07
### Added
- Label about supplier updated to the purchase request card
### Updated
- Invoices UI
### Removed
- Unused widgets
- Svg icons

## [3.0.74] - 2021-10-06
### Added
- Shopping cart product offers screen
- Update cart state after adding to cart from Catalog
### Fixed
- Search service errors handling
- Directly available product info when adding to cart from Catalog
- Filters in catalog overlapped by system UI

## [3.0.73] - 2021-10-05
### Added
- Purchase request product offers screen
- Lint to check trailing commas
- lastDivisionId to the UserModel
### Updated
- Makefile

## [3.0.72] - 2021-10-04
### Added
- Products search aggregations hardcoded config in SearchRequest
- Delivery time filter for products search
### Changed
- Advanced item availability check in products search results including customer id existence at current supplier
- Show corresponding message when search indexes is not ready for current catalog
### Removed
- Cost center from division pattern in product search

## [3.0.71] - 2021-10-01
### Added
- Order again screen
### Removed
- Unused hasUpdates/getUpdates methods
### Updated
- flutter version to 2.5.2
- 3rd party dependencies

## [3.0.70] - 2021-10-01
### Added
- AppBarCartIcon widget to add cart icon in any AppBar actions
- Cart icon added to ProductsSearchScreen and ProductOfferScreen
- "Clear", "Clear all" and "Apply" buttons added to Catalog search filters
- AddToCart button on OrderListProductScreen
### Changed
- OrderListProductQuantityButton replaced with AddToCartButton on OrderListProductCard
### Removed
- OrderListProductQuantityButton as it was replaced with AddToCartButton
### Fixed
- Excess widgets removed from InhouseListProductCard
- Long unit names in InhouseListProductCard
- Product totals update in AddToCartButton
- Unnecessary delays removed from AddToCartButton

## [3.0.69] - 2021-09-30
### Added
- QR-code pdf view to the closed inventory lists
- Origin translation on product details
- Booking approval request reload on product update
### Updated
- Booking approval requests UI
### Changed
- User.setActiveCostCenter method call to the Session.log on costcenter update
### Removed
- User.setActiveCostCenter method

## [3.0.68] - 2021-09-30
### Changed
- Advanced search filters changes according to designs
- Advanced search filters refactoring
- Search will not be performed while selecting filter values inside a single filter
- Perform filtering only when filters where updated
- Allow filter changes when navigation from supplier screen to advanced product search
- AddToCartButton moved to shared
- AddToCartButton now has its own cubit to get product information using Cart.getProductTotals API method
- AddToCartButton will add product to cart using its own cubit if no `onAdd` callback provided
- AddToCartButton adds additional information from Cart.getProductTotals to QuantityBottomSheet
### Added
- Selected filter values will not be removed even if there are no aggregations returned from search API
- Selected filter values with no corresponding aggregations will be displayed disabled
- Selected filter values count for each filter
- Cart.getProductTotals API method
- Cart.deleteItemByPositionId API method
- Availability filter to search only new or only available products in catalog
- 'New' label for product search results
### Fixed
- Long supplier name on ProductCard in advanced search

## [3.0.67] - 2021-09-28
### Added
- Named parameters in translations
- QR-code scanner to the inventory search
- QR-code pdf view to the inventory list
- Inventory items sorting by name
### Removed
- Positional parameters in translations
- Continuous scanning in closed inventories

## [3.0.66] - 2021-09-27
### Added
- Makefile
- Banner to inform user that customer id already requested
- Voice recognition animation for better UX
- Context menus on tap on vertical dots IconButton
### Updated
- Static i18n files
- Imprint screen UI
- Note length for inhouse request and booking from 1000 to 255 characters
- Default filter in inventories to 'Open'
### Removed
- History screen
### Fixed
- Bug with error handling in InhouseListCubit
- Bug with scroll when inhouse request note is long

## [3.0.65] - 2021-09-24
### Added
- Request customer id screen
- Dialogs helper for easier dialog showing
- Pull to refresh feature
- User.setActiveCostCenterId API method integration
- costCenterId parameter in Approvals.approversLookup method call
### Updated
- Old dialogs to be material
- Elevation value in the card theme
- Purchase request product card UI
- Flow after login
- Flow after app reopening
- Cost center selection logic
- Inhouse order approval request UI
- Invoices archive UI
### Fixed
- Issue with keyboard in purchase request chat
### Removed
- Cost center selector on pin screen

## [3.0.64] - 2021-09-21
### Fixed
- Barcode scanner cancel issue
### Changed
- InventoryListScreen, InventoryListItemCard updated according to design
### Added
- Speech recognition for QuantityBottomSheet
- Continuous barcode scanning on InventoryListScreen from floating action button
- Product name on QuantityBottomSheet during continuous barcode scanning
- Speech recognition enabled by default for inventory continuous barcode scanning
- Speech recognition option for inventory in app settings

## [3.0.63] - 2021-09-20
### Updated
- flutter version to 2.5.1
- 3rd party dependencies
- ChatCommentBubble to always show sender name
### Added
- Online suppliers search
- Offer price indication for the product offer and order list product
### Fix
- Issues caused by lint rules

## [3.0.62] - 2021-09-17
### Updated
- Purchase request screen UI
- DisabledBuilder widget
- 3rd party dependencies

## [3.0.61] - 2021-09-16
### Updated
- Advanced search service and repository updated according to search api v2
- search_module data models updated according to search api v2
- Ability to translate item type filter values in advanced search

## [3.0.60] - 2021-09-16
### Updated
- flutter version to 2.5.0
- 3rd party dependencies
### Fixed
- deprecation issues
- build runner warnings

## [3.0.59] - 2021-09-15
### Added
- Adding to list functionality for product offer screen
- Adding to list functionality for search results screen
- Add to cart functionality for search results screen
### Changed
- Filter values are now expandable
- AddToCartButton and AddToList button are now shared inside search_module 
### Fixed
- Price filter selected value display
- Advanced filters count for dynamic filters

## [3.0.58] - 2021-09-13
### Added
- Cart overview screen
- Cart orders to approve screen
- Cart orders to send screen
- Cart orders to merge screen
- Cart sending result screen
### Removed
- Catalog table and related code
- Cart table and related code
- Unused and deprecated code

## [3.0.57] - 2021-09-13
### Updated
- Navigate to new search from supplier detail screen
### Fixed
- First usage of JWT token

## [3.0.56] - 2021-09-13
### Added
- Advanced search service
- Advanced search repository
- JWT token handling and refreshing
- Products Advanced Search screen to replace Catalog screen
- New env variable for search API
- Product offer screen and Cubit
- getOfferById API call
### Updated
- URL for loaded translations
- Advanced filters functionality with Filter mixin and Cubit
- Local translations
- Translation format and scope  
- Catalog screen rote
### Removed
- Catalog screen
- Products Bloc

## [3.0.55] - 2021-09-06
### Updated
- translations format to 'LIST'
- inventory lists UI
### Added
- move to cart button on the purchase request card
### Fixed
- language picker overflow bug

## [3.0.54] - 2021-09-03
### Updated
- 3rd party dependencies
- Scanned documents UI
### Added
- dart_code_metrics package
### Removed
- unused files

## [3.0.53] - 2021-09-02
### Fixed
- Inhouse list products lazy loading
- Inhouse list connection status
- Bug in suppliers cubit
### Updated 
- Checks at supplier screen
- Purchase requests screen UI
- Products search screen UI

## [3.0.52] - 2021-08-31
### Updated 
- Supplier screen UI 

## [3.0.51] - 2021-08-26
## Added
- Float quantity allowance in InhouseListProductModel based on inventoryUnit
## Changed
- Don't allow to send inhouse transfer request without product quantity selected
- Make Note field note mandatory for inhouse transfer request
- Move SEND REQUEST button in SendInhouseTransferRequestScreen to the bottom according to design
- Remove confirmation when leaving SendInhouseTransferRequestScreen
- Allow float values for product quantity in InhouseListProductsList
- Allow deliveryDate in the past in SendInhouseTransferRequestScreen
## Fixed
- Wrap QuantityBottomSheet into a SafeArea
- Fix InhouseListProductsList overlapped by floating action button
- Fix scroll issue in SendInhouseTransferRequestScreen

## [3.0.50] - 2021-08-26
## Changes
- InhouseListProductModel refactored with freezed
- InhouseListProductCard redesigned
- InhouseListScreen redesigned
- InhouseListProductsList redesigned
- QuantityBottomSheet moved to shared  
- OrderListProductQuantityButton changed to use shared QuantityBottomSheet
- InhouseListsRepository changed to use updated InhouseListProductModel
- BookingManageItemCard changed to use shared QuantityBottomSheet
## Added
- InhouseListProductsCubit
- InhouseListProductsFilterConfig for list filtering
- InhouseListDeliveryDate form field to hold DateTime value instead of String
- SendInhouseTransferRequestScreen
- InhouseListProductQuantityButton widget
### Updated 
- Scanned Documents Ui
## Removed
- InhouseListProductBloc
- InhouseListProductTitle widget 
## Fixed
- Fix wrong return value type in QuantityBottomSheet

## [3.0.49] - 2021-08-20
### Added
- LazyLoadingListView widget to place lazy loading logic in one place
- checksum info inside order receiving details
### Updated 
- suppliers screen
### Fixed
- dashboard screen UI

## [3.0.48] - 2021-08-18
### Added
- linter rules
### Updated
- 3rd party dependencies

## [3.0.47] - 2021-08-18
### Added
- book screen
- warehouse entity lookup screen
- booking product screen
### Updated 
- bookings manage screen
- booking manage products screen
### Fixed
- cost centers storing bug

## [3.0.46] - 2021-08-17
### Added
- search for BookingItemsScreen
### Updated 
- Booking Items Screen UI

## [3.0.45] - 2021-08-16
### Updated
- bookings manage screen UI
- cost centers lookup tiles

## [3.0.44] - 2021-08-16
### Added
- adjustments to the bookings overview redesign

## [3.0.43] - 2021-08-16
### Updated
- inventory lists UI without details view

## [3.0.42] - 2021-08-13
### Added
- filters for BookingsOverviewScreen
### Removed
- bookings_overview_search_filters.dart
- bookings_overview_search_query.dart
### Updated
- Bookings Overview Screen Ui [#40423](https://redmine.711media.de/issues/40423)

## [3.0.41] - 2021-08-12
### Changed
- custom ReorderableList to the flutter's build-in ReorderableList

## [3.0.40] - 2021-08-11
### Added
- order lists JSON-RPC methods
### Changed
- order lists code to be online first
### Removed
- shopping lists database tables
- shopping lists JSON-RPC methods
- shopping lists repository

## [3.0.39] - 2021-08-10
### Added
- History cubit [#40284](https://redmine.711media.de/issues/40284)
### Updated
- History screen Ui [#40284](https://redmine.711media.de/issues/40284)

## [3.0.38] - 2021-08-10
### Added
- InhouseListsCubit
- InhouseListsFilterConfig
### Removed
- InhouseListsBloc
### Updated
- InhouseListsScreen redesign

## [3.0.37] - 2021-08-09
### Added
- OrderListProducts screen
- mvp of the OrderListProduct screen
- OrderListSelection screen
- functionality to add order list product to cart and remove from it
### Updated
- OrderListProductsOrderUpdate screen
- SupplierChange screen

## [3.0.36] - 2021-08-05
### Updated
- Pin confirmation screen UI [#40276](https://redmine.711media.de/issues/40276)

## [3.0.35] - 2021-08-04
### Updated
- Message screen UI [#40251](https://redmine.711media.de/issues/40251)

## [3.0.34] - 2021-08-03
### Added 
- Notification cubit [#39970](https://redmine.711media.de/issues/39970)
### Updated
- Notifications screen [#39970](https://redmine.711media.de/issues/39970)

## [3.0.33] - 2021-07-30
### Added
- OrderListsScreen
- OrderListsCubit
### Updated
- SettingScreen
### Removed
- ShoppingListsScreen
- ShoppingListsBloc

## [3.0.32] - 2021-07-28
### Added
- SendMessageToSupplier screen
- email to the UserPreferencesService
- email field to the Sentry
### Fixed
- bug with wrong orders loading after return from the offline
- keyboard type of the username field in the login screen

## [3.0.31] - 2021-07-28
### Updated 
- Dashboard page UI [#39948](https://redmine.711media.de/issues/39948)
- License page UI [#39961](https://redmine.711media.de/issues/39961)
### Added
- Dashboard cubit [#39948](https://redmine.711media.de/issues/39948)

## [3.0.30] - 2021-07-27
### Updated
- Init storage page UI [#39829](https://redmine.711media.de/issues/39829) 
### Added
- Init storage cubit [#39852](https://redmine.711media.de/issues/39852)
### Removed
- Init storage bloc [#39852](https://redmine.711media.de/issues/39852)
### Fixed
- Purchase request currency bug

## [3.0.29] - 2021-07-23
### Updated
- Modules of the app to be as separate screens, not a tab view
- Filter configs to be const
- Some route names
### Removed
- Unused routes

## [3.0.28] - 2021-07-22
### Added
- Orders cubit
- Order products cubit
- Missed icons to the navigation drawer
- Scanned documents menu item in navigation drawer
### Removed
- Orders bloc
- Order products bloc
### Updated
- Orders screen UI
- Order details screen UI
- FreeTextAppBar behavior to use autofocus
- NetworkStateChangedMixin mixin to use ApiStatusBloc directly

## [3.0.27]- 2021-07-21
### Added
- Added reload button to setup screen [#39798](https://redmine.711media.de/issues/39798) 
- Added reload button to sync screen [#39820](https://redmine.711media.de/issues/39820) 
### Updated
- Setup screen UI [#39798](https://redmine.711media.de/issues/39798) 
- Splash screen UI [#39793](https://redmine.711media.de/issues/39793)  
- Sync screen UI [#39820](https://redmine.711media.de/issues/39820) 

## [3.0.26] - 2021-07-21
### Added
- Cost center lookup cubit [#39698](https://redmine.711media.de/issues/39698)
### Updated
- Cost center lookup screen UI [#39697](https://redmine.711media.de/issues/39697)

## [3.0.25] - 2021-07-19
### Removed
- Home screen
### Added
- Navigation drawer
### Fixed
- Bug in cost center picker

## [3.0.24] - 2021-07-19
### Fixed
- Bug in KeyboardVisibility wrapper
### Updated
- Cost center screen UI
- Set pin screen UI

## [3.0.23] - 2021-07-15
### Added
- Filter configuration
- Active filters counter
- Password reset cubit [#39486](https://redmine.711media.de/issues/39486)
### Updated
- Password reset screen [#39485](https://redmine.711media.de/issues/39485)
### Removed
- Password reset bloc 

## [3.0.22] - 2021-07-13
### Added
- WS prefix in login input field
- Password recovery cubit
- Receiving order card
### Fixed
- Not redesigned fields borders
### Removed
- Password recovery bloc

## [3.0.21] - 2021-07-12
### Added
- Global theme
- Receivings module translations
### Updated
- Login Screen [#39427](https://redmine.711media.de/issues/39427)
- Password recovery screen [#39441](https://redmine.711media.de/issues/39441)

## [3.0.20] - 2021-07-09
### Added
- Filter functionality in all receivings module

## [3.0.19] - 2021-07-08
### Added
- Filter functionality

## [3.0.18] - 2021-07-06
### Fixed
- Bug with BehaviorSubject stream value

## [3.0.17] - 2021-07-05
### Added
- Warehouse.categoryLookup method
- Warehouse.packingUnitLookup method
- Integration of the lookups into the app
### Updated
- OrderItemModel

## [3.0.16] - 2021-07-02
### Added
- Warehouse.vatLookup method
- Unified Warehouse lookup screen for store/cost center/cost type/vat lookup
- UI of the order item details
- UI of the order item comments

## [3.0.15] - 2021-07-01
### Added
- Receivings.update method
- Receiving delivery details screen
### Fixed
- Potential memory leak with not disposed FocusNodes
### Updated
- 3rd party dependencies

## [3.0.14] - 2021-06-29
### Added
- Receivings.add method
- New PDF view screen

## [3.0.13] - 2021-06-25
### Added
- Receivings.getOneByOrderId method
- Receivings.Items.lookup method
### Removed
- Old receivings module

## [3.0.12] - 2021-06-23
### Added
- MVP of the receivings module

## [3.0.11] - 2021-06-14
### Updated
- Flutter version to 2.2.2
- WeScan plugin to 1.8.1 version
- IOS platform code to use iOS 12 as a target
- 3rd party dependencies

## [3.0.10] - 2021-06-11
### Added
- Japanese locale support
### Fixed
- UI issues caused by japanese locale
- Bug in translation process
- Usability of the inhouse list place order functionality 

## [3.0.9] - 2021-06-09
### Added
- Example integration test
- Unhandled errors logging
### Updated
- 3rd party dependencies

## [3.0.8] - 2021-05-27
### Updated
- 3rd party dependencies

## [3.0.7] - 2021-05-25
### Added
- Merge branch release-v2.10.8
### Fixed
- Bug causing wrong selecting of the local inventory lists

## [3.0.6] - 2021-05-18
### Added
- Custom realization of the envify plugin

## [3.0.5] - 2021-05-12
### Added
- Environment variables

## [3.0.4] - 2021-04-30
### Refactoring
- Order receivings screens

## [3.0.3] - 2021-04-29
### Add
- Order receivings screen

## [3.0.2] - 2021-04-27
### Fixed
- Some minor bugs

## [3.0.1] - 2021-04-26
### Added
- Update bloc
- Sync bloc
- Messages bloc
### Refactoring
- Theme setting

## [3.0.0] - 2021-04-23
### Refactoring
- Null safety migration
- Login bloc
### Added
- Setup bloc

## [2.10.8] - 2021-05-25
### Fixed
- Bug with comma delimiter in qty field
- Bug in inhouse lists products loading

## [2.10.7] - 2021-04-02
### Fixed
- Bug in pin text field
- Handling of the notification from which app is started
### Updated
- Packages to the null save versions
- Code to fix flutter deprecations

## [2.10.6] - 2021-04-01
### Fixed
- Bug in cart, when deleted products got synced with API
- Bug with ability to paste text into quantity field
- Bug with disabled message of the quantity field, which appear on specific field taps

## [2.10.5] - 2021-03-30
### Updated
- Error codes for debug logging

## [2.10.4] - 2021-03-24
### Updated
- Edge detection plugin

## [2.10.3] - 2021-03-16
### Fixed
- Some platform deprecation issues

## [2.10.2] - 2021-03-15
### Fixed
- Offline mode UI in bookings manage tab
- Tablet UI of the list view on the licenses screen 

## [2.10.1] - 2021-03-12
### Added
- Booking approval request push notifications
- approval_required field in BookingApprovalRequest model
### Fixed
- Bug with wrong preselected next approver in booking request approval process

## [2.10.0] - 2021-03-05
### Added
- Proper filters for online bookings search
### Refactoring
- UI of the booking approval requests
### Removed
- Local bookings storing

## [2.9.31] - 2021-03-01
### Refactoring
- Online bookings search integration
### Added
- Basic functionality for the booking approval requests

## [2.9.30] - 2021-02-25
### Refactoring
- Online inhouse lists search integration

## [2.9.29] - 2021-02-23
### Refactoring
- Some TODOs completion

## [2.9.28] - 2021-02-22
### Added
- Purchase request document deletion

## [2.9.27] - 2021-02-19
### Added
- Purchase request documents loading

## [2.9.26] - 2021-02-15
### Added
- Notification bell icon animation

## [2.9.25] - 2021-02-12
### Fixed
- Push notification model (replaced id parameter in construction from push to be purchase_request_id or invoice_id)

## [2.9.24] - 2021-02-11
### Added
- Ability to retry search in catalog
- Saving of the notifications which came from the background
- Purchase request product qty updated notification handling

## [2.9.23] - 2021-02-10
### Added
- Unit and cost center switching when push notification came from the another unit [#34984](https://redmine.711media.de/issues/34984)
### Fixed
- Bug on purchase request screen when load error happens

## [2.9.22] - 2021-02-09
### Added
- Ability to retry specific actions on purchase request and invoice screens [#34563](https://redmine.711media.de/issues/34563)
### Fixed
- Scrolling physics in FuturelogTabs widget

## [2.9.21] - 2021-02-04
### Fixed
- Blank names in inventory item card and warehouse lookup item card
### Updated
- 3rd party dependencies

## [2.9.20] - 2021-02-04
### Fixed
- Some driver tests
### Added
- Null check of the name field in WarehouseLookupResultItem widget

## [2.9.19] - 2021-02-01
### Added
- Recheck of updates when network connection is back
- Null check of the userName from the LogRecordModel
### Fixed
- Type of the orderQty in InhouseListItemModel from int to double

## [2.9.18] - 2021-01-25
### Fixed
- Bug with sync process stuck on permissions load error
- Conversions to double in fromJson methods of the models
### Added
- Driver test for purchase request item quantity update
- Ability to refresh purchase request on not found error

## [2.9.17] - 2021-01-20
### Fixed
- Permissions in bookings and inhouse lists [#34320](https://redmine.711media.de/issues/34320)
- Bug in purchase request item qty changing
### Updated
- Configurations to build app in profile mode to use SkSl warmup
- 3rd party dependencies
### Added
- Sessions.getPermissions method integration
- Additional permissions checks on cart screens

## [2.9.16] - 2021-01-18
### Fixed
- Bug in the  Suppliers.getDeliveryPlan method parameters
- Bug with wrong parameter type in ProductModel
### Added
- Language parameter in the Bookings.getVoucherUrl method
- Language parameter in the Approvals.Invoice.getInvoiceUrl method
- Language parameter in the Invoices.getInvoiceUrl method
- Language parameter in the Orders.getInvoiceUrl method
- Integration of the Approvals.PurchaseRequest.updateProductQty method

## [2.9.15] - 2021-01-16
### Removed
- Orders offline storing
### Fixed
- Bug with wrong language code in orders products search
- Bug with caching API response data

## [2.9.14] - 2021-01-15
### Updated
- Inventory notification about sync to be less annoying [#34201](https://redmine.711media.de/issues/34201)
- 3rd party dependencies
- Sync label in cart
### Added
- New sync label in inventory
### Fixed
- Issue with wrong language in order products online search

## [2.9.13] - 2021-01-13
### Added
- Inventory item ean deletion feature [#34156](https://redmine.711media.de/issues/34156)

## [2.9.12] - 2021-01-04
### Added
- Online orders search [#23123](https://redmine.711media.de/issues/23123)

## [2.9.11] - 2020-12-21
### Fixed
- Minor security issue (deleting preference when got access denied exception instead of after user click to close error window)
### Refactoring
- Update 3rd party packages

## [2.9.10] - 2020-12-14
### Fixed
- Bug with PDF preview on Android [#33493](https://redmine.711media.de/issues/33493)
- Changelog formatting
- Android release build settings
### Added
- Push application version on sync [#33460](https://redmine.711media.de/issues/33460)
- Sentry debug logging on app specific situations
### Updated
- 3rd party dependencies
- Sentry logging

## [2.9.9] - 2020-11-30
### Fixed
- Search by number in inventory and other lists [#31088](https://redmine.711media.de/issues/31088)
- Bug with search orders (Bestellungen) products by EAN

## [2.9.8] - 2020-11-12
### Added
- Redirect to the products screen on the supplier card tap
### Fixed
- Continue button UI on messages screen on the tablet screen
- Bug with the inventory list unlock by one user on different devices 
- Bug with the ability to send empty order list title on title change
- Driver tests for properly pass messages screen
### Refactoring
- Sentry client setup

## [2.9.7] - 2020-11-09
### Fixed 
- Dialog text buttons overflow issue
- WSW license check for the invoices tab on the approvals
### Removed 
- Barcode scan button near search field in the inventory screen
### Updated
- Shopping list add button
- 3rd party dependencies

## [2.9.6] - 2020-11-04
### Fixed 
- Next approver picker on the last level of the purchase request now hidden
- Bug with wrong data on the approved purchase request card, after moved to cart

## [2.9.5] - 2020-11-04
### Added
- Drive tests for new inventory module
- Warehouse lookup screen title in application bar
### Refactoring
- Bottom bar widget
### Fixed 
- Shopping lists search, when FTS is not supported
- FTS query builder
### Updated
- 3rd party dependencies 

## [2.9.4] - 2020-10-30
### Changed
- "Send" icon in inventories [#32667](https://redmine.711media.de/issues/32667)

## [2.9.3] - 2020-10-30
### Added
- "Barcode scanner" button in the application bar on the inventories screen
- "Send multiple lists" button in the application bar on the inventories screen 
-  UI adjustments for support landscape mode in iPadOS

## [2.9.2] - 2020-10-28
### Added
- Crashlytics preview
- New screen for the approved purchase requests
- Picker to choose a next approver on the purchase requests card
- Sentry debug logs for inventory
- Screens (application bar) titles on most of the screens
### Update
- 3rd party dependencies
- Inventory control module (sync quantity after the change, sync quantities changed in offline mode, etc.)
### Fixed
- Flutter Drive tests

## [2.9.1] - 2020-10-06
### Added
- Remote inventory log (log item quantity changes)
- Sentry debug logs (inventory)
### Changed 
- Application bar collapse when user set focus on quantity field (for prevent overlapping)

## [2.9.0] - 2020-09-30
### Added
- Request approval (next screen after cart), set next approver when possible, and replace switcher with the send button (with send icon)
- When the user has sent all approvals requests, he should see "Your PR(s) has been sent for approval " (instead no results found)
- Purchase request card, remove the status icon, hide unnecessary data (purchase request date), and add the buttons bellow (Approve and Decline)
- Remove approver on purchase request card when this purchase request is assigned to the current user
- On the purchase request cart, instead of PR ID, show "Approval Group" (same as in cart in the web application)
- On the PR details view, replace Approve Level information with a total amount of PR (hotel currency)
- On the PR details view, replace picker "Approve/Decline" on two buttons with the same functionality
- Improved UI of application bar
- On the PRs list screen, we have to hide all ascending sorting options.
- The only current approver can decline or confirm products in PR request
- When a user clicks on the main menu Approval center button, and he has only not approved invoices, then he should go directly to the invoices tab
- After approve or decline user should be redirected to the next assigned purchase request
### Refactoring
- Flutter upgraded to version 1.22 (iOS 14 support)
- Upgraded 3rd party dependencies

## [2.8.2] - 2020-09-15
### Update
- "Open Source Licences" button moved to the bottom of the screen
### Fixed
- Issue with loading in-house lists on each sync even when they were already loaded
### Added 
- Unit tests


## [2.8.1] - 2020-09-08
### Added
- Loading customer logos from Azure Blob Storage (performance improvement)
### Fixed
- Wrong invoice currency [#31489](https://redmine.711media.de/issues/31489)
### Refactoring
- Rewritten toast widget to prevent keyboard overlapping
- Other code adjustments and improvements (https://git.711media.de/moevenpick/futurelog/mobile-application/-/merge_requests/497/commits)

## [2.8.0] - 2020-09-03
### Added
- Invoices notifications
- Licenses screen
### Updated
- Android edge detection library
- Settings screen
### Fixed
- Bug with costCenter and warehouse in booking items
- Driver tests
- Scanned image alignment in the PDF file

## [2.7.9] - 2020-08-24
### Added
- READY_TO_APPROVE filter for invoices in approvals center
- License check on sync
- "Country of origin" on product card in catalog and suppliers products
### Updated
- Offline translations
### Fixed
- Maximum message length in chat
- Only order lists permission check [#31113](https://redmine.711media.de/issues/31113)

## [2.7.8] - 2020-08-19
### Added
- Scanned document preview before send
- Upload of scanned documents

## [2.7.7] - 2020-08-17
### Added
- Invoices scanner preview

## [2.7.6] - 2020-08-10
### Added
- Barcode scanner icon on order lists screen
- Dialog message when user tap on a protected menu item (no license or no permissions)
### Removed
- Barcode scanner icon on approvals and invoices screens
### Fixed
- Reset of not approved items count after division switch
- No account assignment information on the invoice details screen
### Refactoring
- Update flutter to 1.20.1
- Update dependencies to the latest stable version

## [2.7.5] - 2020-08-05
### Added
- Backup and restore of the sensitive data on database upgrade
- Driver tests
- Invoices archive module
### Changed
- Home screen bottom labeled buttons to the icons (PoC)
- Permission guard widget to support license type check
- Inventory list progress bar (calculation of counted items)
### Fixed
- Bug with PDF file loading
- Duplicated records in inventory log [#30647](https://redmine.711media.de/issues/30647)

## [2.7.4] - 2020-07-14
### Fixed
- Search by ID in inventory lists
- SQL error after sending cart on Android (removed "AS" keyword in delete query)

## [2.7.3] - 2020-07-13
### Fixed
- Push notification on iPhone
- When the user was on messages screen and there was no internet connection he continuously getting dialog with an alert
### Added
- Switching on swipe between tabs on the PR details view

## [2.7.2] - 2020-07-09
### Fixed
- Invoice approval process
- Push notifications (PRs)

## [2.7.1] - 2020-07-09
### Added
- Invoice request approval and approve
- Invoice reset
- Invoice filters

## [2.7.0] - 2020-07-03
### Added
- Invoices approval
- Last comment and requester info on PRs card
- Configuration option to enable/disable confirmation dialogs
### Refactoring
- Errors handling
### Fixed
- Cart clean after sending order
- Other minor fixes

## [2.6.1] - 2020-05-27
### Added
- Notifications center (the special screen where all missed notifications will appear) and counter of missed notifications
- "Move To Cart" on PR screen (useful when a user opens PR from notification)
- Changing of "approve" button label text to "Approve" or "Decline" depends  on the option selected in status switcher (should help to avoid mistakes)
### Changed
- Improved performance and responsiveness of PRs chat, to make it look more like messengers (work yet in progress)
- Approvals center UI/UX became more consistent with other applications (paddings, font sizes, etc.)
### Fixed
- SQL bug that happened sometimes in in-house lists (when cost center was NULL)
- The issue with catalog search (in some rare cases users got an error when trying to search by two words)
- Bug when comment for supplier from past order was not cleared and appears in next order
- "On/Off" switcher now has same colors independently from the platform (red - off, green - on)
### Refactoring
- Updated dependencies to latest stable versions and related refactoring (firebase_messaging, flutter_markdown, get, path_provider, url_launcher, barcode_scan)

## [2.6.0] - 2020-05-24
### Added 
- Approvals center (purchase requests)
- Sending orders

## [2.1.0 - 2.1.5]
This versions has been skip by mistake, and it's already too late to change this

## [2.0.53] - 2020-04-30
### Added 
- Indicator to the inventory list that has been transmitted (+ filters) [#29161](https://redmine.711media.de/issues/29161)
- Saving quantity in the inventory list when the input field loses focus (on blur)
- Warning message after login when the user does not belong to any division

### Changed
- Success sign instead confirmation pop-up when the user has sent inventory list
- Inventory log icon (old one was not properly aligned)

## [2.0.52] - 2020-03-30
### Added 
- Bulk recovery of the latest values in the inventory list. 
Test case: 
    1) Open a list 
    2) Change values 
    3) Reset & re-open list (simulate data lost) 
    4) Recover from log
### Fixed
- Search booking item by EAN
- Missed cost center name on booking item card

## [2.0.51] - 2020-03-26
### Fixed
- Regression of issue with missed keypad after EAN scan [#27372](https://redmine.711media.de/issues/27372)
### Added
- Log of inventory bookings (saving changes, recovery single change, recovery all - yet to be implemented)

## [2.0.50] - 2020-03-23
### Fixed
- Loading application after update (there was issue with writing NUL values in secure storage)
- FTS search on android

## [2.0.49] - 2020-03-23
### Fixed
- Supplier products loading (in some cases there were products without name in list)
### Updated
- Dependencies/Packages
- Driver tests
- Unit tests
### Refactoring
- Performance improvements

## [2.0.48] - 2020-03-16
### Fixed
- Switch to tablet layout for 7'' screen
- Sorting of read-only order lists now is not possible
- Orders updates (was not working)
- It was possible to bypass pin confirmation in cart
### Updated
- Dependencies/Packages
- Driver tests
- Unit tests

## [2.0.47] - 2020-03-03
### Changed
- Improved loading speed of inventory lists

## [2.0.46] - 2020-03-02
### Changed
- Now user can confirm 0 quantity in the inventory list
- When user set Qty to 0 (in the catalog, order list, etc.) for the product that is in the cart he will be asked if he wants to delete the product from the cart
 from cart  

## [2.0.45] - 2020-02-26
### Added
- Tablet optimizations
- Division order days on supplier card (instead of delivery days)
### Changed
- Calendar screen instead iOS date picker
### Removed
- Filling primary logo with white color
- Add to cart button in order lists
### Fixed
- Inventory disappearing after lock
- Orders loading

## [2.0.44] - 2020-02-19
### Add
- Integration with API v3.x (better security, fixed issues with deadlocks)
### Fixed
- Cost center problem [#27623](https://redmine.711media.de/issues/27623)
- Closed inventories still in open inventories [#27626](https://redmine.711media.de/issues/27626)
- Unit swap Inhouse Transfer [#27627](https://redmine.711media.de/issues/27627)
- Other minor issues

## [2.0.43] - 2020-02-10
### Fixed
- Search input clean after scan EAN [#27372](https://redmine.711media.de/issues/27372)
### Changed
- Allow decimal values for all units in inventory [#27363](https://redmine.711media.de/issues/27363)
### Added 
- Automatic testing with Flutter 
- "Back button" on settings screen [#27374](https://redmine.711media.de/issues/27374)
- Password policy information (i18n) on "set new password" screen [#27377](https://redmine.711media.de/issues/27377)
- Password recovery and password reset now support user ID without prefix 'WS' [#26158](https://redmine.711media.de/issues/26158)
- Application version check 


## [2.0.42] - 2020-01-28
### Changed
- Undo UI (marked item instead pop-up)
### Fixes
- Stock quantity rounding for booking items
- Issue when quantity field does not get empty on first entry
- Issue when the user was able to enter a decimal value for not decimal units (e.g. Boxes)
- Stock quantity rounding in bookings
- The booking delivery date has been set to the current date
### Added
- Cart icon on quantity field to indicate that product is in cart

## [2.0.41] - 2020-01-10
### Fixes
- [Tablet] UI (login, calculator, inventory list signature, etc.)
- [Tablet] Prevent hiding logo on login when keyboard is open (we have enough size on tablet to show logo)
- New empty order list was not available in app after update data from server
- Further refactoring and optimizations
### Added
- Terminate user session from admin
- Undo delete operation (preview, at the moment only cart items and order lists items), history screen
- "Go to catalog button" when no products on shopping cart (same as in order lists)
### Know issues
- [Tablet] when number of products in cart > 99 there is not enough space to show all numbers on cart icon

## [2.0.40] - 2020-01-10
### Added
- Notification on login when no connection (instead wrong user login/password message)
- New dialog icon
### Fixed 
- Reset language whe user sign out


## [2.0.39] - 2020-01-07
### Fixed
- Application fonts (changed to original one from design)
### Refactoring
- i18n loading (reduced server requests)
- Data synchronization
### Added
- Custom pop-ups (preview)
- New icon fo statuses (uploaded product, delivered inhouse order, etc.)
- Animated success icon

## [2.0.38] - 2019-12-05
### Fixed
- Wrong messages for clients [#26159](https://redmine.711media.de/issues/26159)
- Delivered amount [#26160](https://redmine.711media.de/issues/26160)
### Changed
- Font for available quantities in inhouse order [#26161](https://redmine.711media.de/issues/26161)
- Removed not needed elements from dashboard
- Temporarily disabled inventory module
- Limit of loaded (delivered and deleted bookings) booking items from 500 to 5000 for last two month

## [2.0.37] - 2019-12-04
### Changed
- Bookings management screen
- Dashboard
### Added 
- Messages
- Bookings overview screen
- New home screen (drawer)
### Refactoring
- PinBox widget

## [2.0.36] - 2019-11-20
### Fixed
- Sync issue for divisions (units / Betriebe) that have locale "en_GB"
- SQL error when deleting bookings on update
- Error when sending booking with empty comment
- Error when booking item cost center (or any other field) has "0" value
- Other minor fixes and improvements

## [2.0.35] - 2019-11-20
### Added
- Bottom bar menu after cart confirmation [#25725](https://redmine.711media.de/issues/25725)
- SVG icons (high-resolution icons), except EAN icon and some other icons when we need design
- Integration of sentry.io (debug mode)
- API health check instead network connection check (should solve warning with location permissions)
- Added "Retry" button below the sync error message
- Real server time in settings (before was time from the device)
- Further refactoring and performance optimizations

### Fixed
- Offline mode item search [#25733](https://redmine.711media.de/issues/25733)

## [2.0.34] - 2019-11-14
### Added
- Bookings (first stable release)
- Users sessions tracking (needed for delete data from admin) + admin interface (demo)
- "Success sign" everywhere after confirmation
- Automatic collapse header when focus on quantity field (e.g. cart)
- Other minor changes and improvements

### Fixed (incl. change requests)
- wrong unit in order list [#25663](https://redmine.711media.de/issues/25663)
- Order list sorting [#25653](https://redmine.711media.de/issues/25653)
- Order lists multiple times [#25651](https://redmine.711media.de/issues/25651)
- Minimum order quantity wrong [#25646](https://redmine.711media.de/issues/25646)
- Quantity in cart - units other way around [#25644](https://redmine.711media.de/issues/25644)
- Filter Available only - no result [#25640](https://redmine.711media.de/issues/25640)
- Sorting supplier A-Z [#25639](https://redmine.711media.de/issues/25639)

## [2.0.33] - 2019-11-11
- (Shopping cart) Use the same quantity fields (with the numeric keyboard) as for inventory.
- (Shopping cart) On the cart overview page (step 1), the "Continue" button must always be shown below the "Cart" label, even - - (Shopping cart) when the header is collapsed
- (Shopping cart) On the cart summary page (step 2), Fix bug with wrong currency code beside "division price" when switching cost centers
- (Shopping cart) On the cart summary page (step 2), the supplier name font size must be increased, "supplier" label above must be removed
- (Shopping cart) On the cart summary page (step 2), instead of label "X items" above the division currency, add the label "Division currency."
- (Shopping cart) On the cart summary page (step 2), in the bottom bar, change label "X items" (cart.count_of_items) on "X orders" (cart.count_of_orders)
- (Shopping cart) On the cart summary page (step 2), add "envelop/message" icon to "Add message for supplier" button (on the left side1)
- (Shopping cart) On the cart summary page (step 2), add "lists" icon to "Show items" button (on the left side1)
- (Order list) Label "Edit" beside list name must be replaced with "Pencil" icon
- (Order list) The "Sort items button" must be replaced on the icon in the bottom bar (similar to the print icon in invoice view)
- (In-House list) Use same quantity field (with the numeric keyboard) as for inventory
- (In-House list) Label "Edit" beside list name must be replaced with "Pencil" icon
- (In-House list) On the in-house list item card, the four-digit store code in front target/source store name must be removed
- (In-House list) Change "label key" for the label "Add message for supplier" (to inhouse_list.add_order_note), so it can be translated in the context of the in-house list
- (Inventory) List detail view, header must not collapse when a user scrolls the list (search is essential for inventory so it must always be visible)
- (Inventory)  Inventory item card must be collapsible (same as order list card), EAN codes must be located "inside" card
- (Inventory)  On inventory item card, instead of label "Menge eingeben" show inventory unit (e.g., kilogram). The standalone inventory unit field must be removed.
- (Catalog) Bug with the translation of label "ab", in English must be "from"
- (Catalog) On the product details page, must be removed gradient background of the product image and the first item in the list
- (Catalog)  On the product detail page, MPNr (meta_id) number font size must be decreased
- The collapsible header must expand when a user starts scrolling top (not when the list has reached the top as it's now)
- The Turkish and the Hungarian languages shall be removed from languages switcher
- Move calculator icon (now it's available besides the quantity field) to the bar above numeric keyboard that shown when a user changes the quantity
- Further bookings adjustments (details list, filters)


## [2.0.32] - 2019-10-31
- Update sync workflow
- Update calculator, allow empty value
- Add price in supplier currency on cart summary
- Bugfixing

## [2.0.31] - 2019-10-30
- Fixed search for suppliers, orders, shopping lists, etc.
- Search supplier products by whole catalog (online search) not just offline products (user has to be online)
- Open main menu (home screen) instead of the dashboard
- Products within an application now show quantities in cart (in case of the product has been added to cart)
- Set new password in the application (UI, API, integration)
- Fixed price formatting (removed padding before price)
- Convert product prices to division currency in the cart total price
- Fixed autofocus on the pin confirmation screen
- Cart icon always clickable
- Hide button for creating new IH list
- Refresh the list of cost center from the server when user going to change cost centers (user has to be online)
- Fixed issue with the broken image when adding products to shopping (order) lists
- Added button "Go to search to add products" when the shopping list is empty
- Lists are sorted by last used in lists picker (when adding product to shopping (order) lists)
- Lists are shown for whole division by default (before was shown only cost center lists)
- Fixed sorting by most used (by the count of views) in shopping (order) lists
- Added draggable are from the left side on cards when sorting shopping (order) list item (we didn't make the whole area draggable because it makes scrolling of list very hard)
- Quantity in cart getting overwritten instead summing up (when adding the same product again)
- Reset quantity (set 0) in the calculator
- Added highlighting (bottom border) to input when editing shopping (order) list name
- Fixed sorting by "newest" first in cart
- Added badge to the "uploaded" product cards in cart (should be replaced with something more "fancy"; waiting for feedback from the designer)
- API adjustments for "secondary logo" (user branding)
- Reducing empty space on list cards (partly / in progress)
- Added "KundenMandant" field in branding settings (API admin)
- Exclude from search/sync products without customer number (CATXXXXXX.KndNr IS NOT NULL)
- Numeric keyboard and calculator icon beside quantity field in the inventory item card
- Show supplier notification when not empty on the cart summary screen
- Show supplier notification when not empty in in-house list
- Bookings (Lagerbuchnungen) - API, loading data on a sync, basic list overview
- Fixed issue in the in-house list when search reset added quantity
- Secondary logo (custom home screen logo)

## [2.0.30] - 2019-10-16
- Add API integration for password recovery screen
- UI fixes (cart overview, pushing same screen multiple time, padding)
- Other minor fixes

## [2.0.29] - 2019-10-14
- Add functionality to add item in in-house lists
- Fix cart summary
- Add local data updates
- Add API errors handling


## [2.0.28] - 2019-09-26
- Fix issue with sync of favorite suppliers
- Fix issue with adding shopping list to cart when catalog is empty
- Shopping lists API integration
- Increased font sizes (experimental)

## [2.0.27] - 2019-09-23
- Translations fixes
- Permissions integration for shopping lists and cart
- Fix login/-pin UI (scroll when keyboard is open)
- Added password recovery form
- Added workaround for custom logo issue (white box instead of logo)
- Added search by supplier products (now real products is shown instead of "meta" products)

## [2.0.26] - 2019-09-16
- Adjust login workflow (add authorization error messages when account is locked, suspended or password change is required)
- Fix issue with offline mode and translations (black screen on app start in offline mode)
- Further performance optimizations 

## [2.0.25] - 2019-09-13
- Further performance optimization
- Further translations fixes
- Fixed expansible headers (open on scroll down)
- Fixed issue with empty card when no translations for products
- Added customer branding integration

## [2.0.24] - 2019-09-11 | Demo Release
- Translations
- Refactoring (breaking changes)

## [2.0.23] - 2019-09-09
### Added
- Translations

## [2.0.22] - 2019-09-06
### Added
- Translations

## [2.0.21] - 2019-09-04
### Added
- Send multiple inventory lists at once
- Save (sync) new shopping lists on the server
- Update (sync) shopping list on the server on rename
- Save (sync) new in-house lists on the server
- Update (sync) in-house list on the server on rename
- Check permissions to access inventory module
- Adjusted offline catalog search to make it compatible with an online search (search by MPNr/ArtNr)
- Added FTS search for orders and cart items
- Added "offline mode wrapper" for all actions that require the application to be online

## [2.0.20] - 2019-08-26
### Added
- Minor adjustments to pass raw search query to API (support of search by MPNr/ArtNr), fine tuning

## [2.0.19] - 2019-08-19
### Added
- Added offline full-text search to shopping lists (Bestelllisten), in-house lists and inventory (remains orders and cart)
- Search condition has been changed too AND instead OR (all words has to be present in text)
- Added catalog online search (easiest way to switch online/offline search is to turn on/off airplane mode)

## [2.0.18] - 2019-08-16
### Added
- Removed info bar (info about a counted product) on the inventory list details screen
- On inventory list details screen button "Product hinzufügen" has been implemented as an icon beside EAN search icon
- "Liste freigeben" button renamed to "Bearbeitung abbrechen" and moved to list card in general overview
- "Liste absenden" button has been moved on list card in general overview
- Implemented API method Catalog.rebuild (execute UpdateCatalog procedure) and added this API method call in application before sync
- Implemented filtering of products with OfflineMode = 'X' on initial loading 
- Performance optimizations in inventory lists
- Fixed issue with org unit (Betrieb) 2424 (User WS0020), inventory was not loaded due to big size (> 222k of inventory items), added condition to load only closed inventories not older than one year
- Implemented offline fuzzy full text search in application (at the moment only catalog and suppliers)
- Implemented products by favorite suppliers (on product details screen)

## [2.0.17] - 2019-08-08
### Added
- Fixed adding items to inventory lists (now selecting items from WSW_ITEM) [FL-014]
- Increased speed of inventory list updates on open (10x or more) [FL-014]
- Various bugfixes for inventory lists, incl. proper german labels (mostly), fixes for add EAN function, updated signature screens, etc. [FL-014]
- Inhouse lists adjustments, create a new list (w/o sync), edit list title (w/o sync), ordering (umlagerung anfordern) incl. delivery date picker and comment (with API) [FL-023]
- Calendar widget to select a delivery date on cart summary screen [FL-017]

## [2.0.16] - 2019-08-05
### Added
- Inventory module [FL-014]
- Shopping Lists (Bestellisten) adjustments [FL-012]

## [2.0.15] - 2019-07-25
### Added
- Order PDF preview [FL-029]
- PIN confirmation screen and PIN restriction rules [FL-016]
- Switch cost center [FL-001]
- Deleting shopping list items with dismission (swipe left) and confirmation popup [FL-012]
- Hide cost centers lookup when only one cost center is presented [FL-042]
- Call API method User.logout on logout from app [FL-027]

## [2.0.14] - 2019-07-18
### Added
- Products filtering by favorite suppliers
- On supplier card added button "Show products"
- Shopping list items sorting (without sync)
- Shopping list collapsible header on scroll
- Dashboard chart for "last orders for 7 days"
- UI fixes (reduced number of used font sizes, all font sizes now collected in one place, buttons not "cut" text label, etc.)
- Refactoring of main navigation (drawer)
- Confirmation pop-up on logout

## [2.0.13] - 2019-07-15
### Added
- Possibility to mark supplier as a favorite, as well as filtering, only favorites suppliers

### Fixed
- Fix calculator buttons off-screen on small devices

## [2.0.12] - 2019-07-10
### Fixed
- Top suppliers chart error when no data available

## [2.0.11] - 2019-07-09
### Fixed
- Shopping list queries (product duplicate issue)
- Cart items quantity calculation

### Added
- Added save cart via API

## [2.0.10] - 2019-07-09
### Added
- Cost centers lookup
- Dashboard charts (except orders by days)
- Dashboard messages
- Store shopping list item quantity until an order is placed
- Fix sql queries, add unit_id/cost_center_id condition

## [2.0.9] - 2019-07-05
### Added
- Cart summary screen and totals calculation
- Add missed german translations
- Round quantity to int when the fraction is 0
- Remove not needed widgets on the dashboard
- Add title and NoCuPerOu on the calculator screen
- Round calculator values
- Real pictures from FutureLog catalog
- Clickable URL/phone on supplier card 
- Highlighted non-empty quantity field
- Saving of all preferences in secure storage
- Add error message when a PIN is wrong
- Fix sync issue when orders are empty
- Fix issues with changing  supplier

## [2.0.8] - 2019-06-30
### Added
- Functionality to change product supplier moevenpick/futurelog/tasks#76 (preview!)
- Functionality to create a new shopping list moevenpick/futurelog/tasks#101 (without editing)
- Copy item to another shopping list moevenpick/futurelog/tasks#109
- Move item to another shopping list moevenpick/futurelog/tasks#110

## [2.0.7] - 2019-06-21
### Added
- Feature moevenpick/futurelog/tasks#97 delete product from shopping list
- Feature moevenpick/futurelog/tasks#98 add shopping list to cart
- Feature moevenpick/futurelog/tasks#133 change product qty in cart
- Feature moevenpick/futurelog/tasks#76 add product to shopping list
- Feature moevenpick/futurelog/tasks#96 add products to cart
- In-house lists moevenpick/futurelog/tasks#86 moevenpick/futurelog/tasks#87

## [2.0.6] - 2019-06-18
### Added
- Collapsed headers for product details and order details screen
- Order details screen
- Latest orders and last modified shopping lists on catalog dashboard
- Dashboard as the default screen in catalog module
- Shopping cart overview (rough implementation)
- Shopping list details

## [2.0.5] - 2019-06-14
### Added
- Product search screen and product details
- Shopping lists search (preview)
- Orders list search (preview)
- Inventory lists search and details (preview)

### Update
- Database schema for compatibility with new API version