import 'package:app/i18n/labels_config.dart';
import 'package:app/receivings_module/models/filter/config.dart';
import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:flutter/material.dart';

const INVOICES_FREE_TEXT_FID = '1633424065';

const INVOICES_SORTING_FID = '1633424066';
const INVOICES_SORT_BY_INVOICE_DATE_ASC = '1633424067';
const INVOICES_SORT_BY_INVOICE_DATE_DESC = '1633424068';
const INVOICES_SORT_BY_REQUESTED_AT_ASC = '1633424069';
const INVOICES_SORT_BY_REQUESTED_AT_DESC = '1633424070';
const INVOICES_SORT_BY_UPLOADED_AT_ASC = '1633424071';
const INVOICES_SORT_BY_UPLOADED_AT_DESC = '1633424072';
const INVOICES_SORT_BY_SUPPLIER_NAME_ASC = '1718639686';
const INVOICES_SORT_BY_SUPPLIER_NAME_DESC = '1718639687';

const INVOICES_FILTER_STATUS_FID = '1633424073';
const INVOICES_FILTER_STATUS_ASSIGNED_TO_ME = '1633424074';
const INVOICES_FILTER_STATUS_READY_TO_APPROVE = '1633424075';

const INVOICES_DATE_RANGE_FID = '1633424076';

const INVOICES_INVOICE_ID_FID = '1660058850';
const INVOICES_SUPPLIER_INVOICE_ID_FID = '1660058851';
const INVOICES_SUPPLIER_NAME_FID = '1660058852';

class InvoicesFilterConfig extends FilterConfig {
  InvoicesFilterConfig({required bool isForUniversalApprover}) {
    _advancedFilterConfig = [
      FilterModel.list(
        id: INVOICES_FILTER_STATUS_FID,
        label: Labels.approvals.invoices.filter.status.title,
        values: [
          FilterValueModel<String>(
            value: INVOICES_FILTER_STATUS_ASSIGNED_TO_ME,
            label: Labels.approvals.invoices.filter.status.assigned_to_me,
          ),
          if (!isForUniversalApprover)
            FilterValueModel<String>(
              value: INVOICES_FILTER_STATUS_READY_TO_APPROVE,
              label: Labels.approvals.invoices.filter.status.ready_to_approve,
            ),
        ],
      ),
      FilterModel.dateRange(
        id: INVOICES_DATE_RANGE_FID,
        label: Labels.invoices_archive.filter.status.date_range,
      ),
      FilterModel.text(
        id: INVOICES_INVOICE_ID_FID,
        label: Labels.approvals.invoices.filter.invoice_id.label,
        hintText: Labels.approvals.invoices.filter.invoice_id.hint_text,
        maxLength: 50,
      ),
      FilterModel.text(
        id: INVOICES_SUPPLIER_INVOICE_ID_FID,
        label: Labels.approvals.invoices.filter.supplier_invoice_id.label,
        hintText:
            Labels.approvals.invoices.filter.supplier_invoice_id.hint_text,
        maxLength: 255,
      ),
      FilterModel.text(
        id: INVOICES_SUPPLIER_NAME_FID,
        label: Labels.approvals.invoices.filter.supplier_name.label,
        hintText: Labels.approvals.invoices.filter.supplier_name.hint_text,
        maxLength: 255,
      ),
    ];
  }

  final QueryFilterModel _freeTextFilterConfig = QueryFilterModel(
    id: INVOICES_FREE_TEXT_FID,
    label: Labels.filter.search,
    eanScannerEnabled: false,
  );

  final ListFilterModel _sortingFilterConfig = ListFilterModel(
    id: INVOICES_SORTING_FID,
    label: Labels.filter.sorting,
    values: [
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_INVOICE_DATE_ASC,
        label: Labels.approvals.invoices.filter.sorting.by_invoice_date_asc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_INVOICE_DATE_DESC,
        label: Labels.approvals.invoices.filter.sorting.by_invoice_date_desc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_REQUESTED_AT_ASC,
        label: Labels.approvals.invoices.filter.sorting.by_requested_at_asc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_REQUESTED_AT_DESC,
        label: Labels.approvals.invoices.filter.sorting.by_requested_at_desc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_UPLOADED_AT_ASC,
        label: Labels.approvals.invoices.filter.sorting.by_upload_date_asc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_UPLOADED_AT_DESC,
        label: Labels.approvals.invoices.filter.sorting.by_upload_date_desc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_SUPPLIER_NAME_ASC,
        label: Labels.approvals.invoices.filter.sorting.by_supplier_name_asc,
      ),
      FilterValueModel<String>(
        value: INVOICES_SORT_BY_SUPPLIER_NAME_DESC,
        label: Labels.approvals.invoices.filter.sorting.by_supplier_name_desc,
      ),
    ],
  );

  late List<FilterModel> _advancedFilterConfig;

  final Map<String, FilterValueModel> _defaultFilterValues = {
    INVOICES_FREE_TEXT_FID: const FilterValueModel<String>(value: ''),
    INVOICES_SORTING_FID: FilterValueModel<String>(
      value: INVOICES_SORT_BY_REQUESTED_AT_DESC,
      label: Labels.approvals.invoices.filter.sorting.by_requested_at_desc,
    ),
    INVOICES_FILTER_STATUS_FID: FilterValueModel<String>(
      value: INVOICES_FILTER_STATUS_ASSIGNED_TO_ME,
      label: Labels.approvals.invoices.filter.status.assigned_to_me,
    ),
    INVOICES_DATE_RANGE_FID: const FilterValueModel<DateTimeRange?>(
      value: null,
    ),
    INVOICES_INVOICE_ID_FID: const FilterValueModel<String>(
      value: '',
    ),
    INVOICES_SUPPLIER_INVOICE_ID_FID: const FilterValueModel<String>(
      value: '',
    ),
    INVOICES_SUPPLIER_NAME_FID: const FilterValueModel<String>(
      value: '',
    ),
  };

  @override
  QueryFilterModel get freeTextFilterConfig => _freeTextFilterConfig;

  @override
  ListFilterModel get sortingFilterConfig => _sortingFilterConfig;

  @override
  List<FilterModel> get advancedFilterConfig => _advancedFilterConfig;

  @override
  Map<String, FilterValueModel> get defaultFilterValues => _defaultFilterValues;
}
