import 'package:app/approvals/cubits/invoices/cubit.dart';
import 'package:app/approvals/models/approval_request.dart';
import 'package:app/approvals/models/approver_lookup_config.dart';
import 'package:app/approvals/widgets/approver_selector.dart';
import 'package:app/approvals/widgets/invoices/invoices/action_buttons.dart';
import 'package:app/approvals/widgets/invoices/invoices/file_buttons.dart';
import 'package:app/approvals/widgets/invoices/invoices/status_badge.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/invoices_archive_module/models/invoice.dart';
import 'package:app/invoices_archive_module/screens/tax_breakdown.dart';
import 'package:app/invoices_archive_module/widgets/credit_note_tag.dart';
import 'package:app/invoices_archive_module/widgets/type_tag.dart';
import 'package:app/shared/config/routes.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/tags_row.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_symbols_icons/symbols.dart';

class InvoiceCard extends StatelessWidget {
  final InvoiceModel invoice;

  final String userId;

  final bool showDivision;

  final int index;

  const InvoiceCard({
    Key? key,
    required this.invoice,
    required this.userId,
    required this.showDivision,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tr = context.translator;

    return GestureDetector(
      onTap: () async {
        final cubit = context.read<InvoicesCubit>();

        await Navigator.of(context).pushNamed(
          INVOICE_SCREEN_ROUTE,
          arguments: {
            'invoice': invoice,
            'id': invoice.invoiceId,
            'divisionId': invoice.divisionId,
          },
        );

        cubit.reload();
      },
      child: FLCard(
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: FLSpacings.xsm,
                vertical: FLSpacings.sm,
              ),
              child: Column(
                children: [
                  FLCardTitle(
                    tag: TagsRow(
                      tags: [
                        InvoiceTypeTag(
                          type: invoice.type,
                        ),
                        if (invoice.amountNetto < 0 || invoice.amountBrutto < 0)
                          const InvoiceCreditNoteTag(),
                      ],
                    ),
                    id: invoice.supplierInvoiceId,
                    title: invoice.supplier ?? '-',
                    subtitle: df(context, invoice.invoiceDate),
                  ),
                  if (showDivision)
                    FLDataText(
                      data: invoice.divisionLabel,
                      size: FLDataTextSize.small,
                    ),
                  FLLabeledTextHorizontal(
                    label: tr(
                      Labels.approvals.invoices.invoice.amount_netto,
                    ),
                    text: $(
                      context,
                      invoice.amountNetto,
                      currency: invoice.currency,
                    ),
                    size: FLLabeledTextSize.small,
                  ),
                  if (!invoice.isInUnitCurrency)
                    FLLabeledTextHorizontal(
                      label: '',
                      text: $(
                        context,
                        invoice.amountNettoDivisionCurrency,
                        currency: invoice.unitCurrency,
                      ),
                      textColor: (colors) => colors.gray600,
                      size: FLLabeledTextSize.small,
                    ),
                  FLLabeledTextHorizontal(
                    label: tr(
                      Labels.approvals.invoices.invoice.amount_tax,
                    ),
                    text: $(
                      context,
                      invoice.amountBrutto - invoice.amountNetto,
                      currency: invoice.currency,
                    ),
                    size: FLLabeledTextSize.small,
                    action: FLLabeledTextActionButton(
                      symbol: const FLSymbol(Symbols.info),
                      onPressed: () => Navigator.of(context).push(
                        InvoiceTaxBreakdownScreen.route(
                          invoiceId: invoice.invoiceId,
                          invoiceDivisionId: invoice.divisionId,
                        ),
                      ),
                    ),
                  ),
                  if (!invoice.isInUnitCurrency)
                    FLLabeledTextHorizontal(
                      label: '',
                      text: $(
                        context,
                        invoice.amountBruttoDivisionCurrency -
                            invoice.amountNettoDivisionCurrency,
                        currency: invoice.unitCurrency,
                      ),
                      textColor: (colors) => colors.gray600,
                      size: FLLabeledTextSize.small,
                    ),
                  FLLabeledTextHorizontal(
                    label: tr(
                      Labels.approvals.invoices.invoice.amount_brutto,
                    ),
                    text: $(
                      context,
                      invoice.amountBrutto,
                      currency: invoice.currency,
                    ),
                    size: FLLabeledTextSize.small,
                  ),
                  if (!invoice.isInUnitCurrency)
                    FLLabeledTextHorizontal(
                      label: '',
                      text: $(
                        context,
                        invoice.amountBruttoDivisionCurrency,
                        currency: invoice.unitCurrency,
                      ),
                      textColor: (colors) => colors.gray600,
                      size: FLLabeledTextSize.small,
                    ),
                  if (invoice.lastLogMessage != null) ...[
                    const FLDivider(
                      topPadding: FLSpacings.xsm,
                      bottomPadding: FLSpacings.xsm,
                    ),
                    FLDataText(
                      data: invoice.lastLogMessage!,
                      size: FLDataTextSize.small,
                    ),
                  ],
                  if (invoice.hasComment) ...[
                    const FLDivider(
                      topPadding: FLSpacings.xsm,
                      bottomPadding: FLSpacings.xsm,
                    ),
                    FLCardComment(comment: invoice.invoiceComment!),
                  ],
                  if (invoice.canApprove(userId) && !invoice.isLastLevel) ...[
                    const FLDivider(
                      topPadding: FLSpacings.sm,
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 10, 29, 10),
                      child: ApproverSelector.labeled(
                        label: tr(
                          Labels.approvals.invoices.invoice.next_approver_label,
                        ),
                        approver: invoice.nextApprover,
                        config: ApproverLookupConfig(
                          id: invoice.invoiceId,
                          type: ApprovalRequestType.INVO.name,
                          level: invoice.nextLevel!,
                          overwriteDivisionId: invoice.divisionId,
                          overwriteCostCenterId: null,
                        ),
                        onApproverSelected: (approver) {
                          context
                              .read<InvoicesCubit>()
                              .updateApprover(index: index, approver: approver);
                        },
                      ),
                    ),
                  ],
                  FLGaps.xsm,
                  InvoiceCardFileButtons(invoice: invoice),
                  FLGaps.sm,
                  InvoiceCardActionButtons(
                    index: index,
                    invoice: invoice,
                    userId: userId,
                  ),
                ],
              ),
            ),
            InvoiceStatusBadge(invoice: invoice),
          ],
        ),
      ),
    );
  }
}
