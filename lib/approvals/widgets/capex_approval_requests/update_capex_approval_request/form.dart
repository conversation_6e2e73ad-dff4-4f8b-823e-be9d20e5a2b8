import 'package:app/approvals/cubits/update_capex_approval_request/cubit.dart';
import 'package:app/approvals/models/approver_lookup_config.dart';
import 'package:app/approvals/widgets/approver_selector.dart';
import 'package:app/approvals/widgets/capex_approval_requests/update_capex_approval_request/comment_field.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/shared/helpers/helpers.dart';
import 'package:app/shared/widgets/fl_ui_wrappers/status_notification.dart';
import 'package:app/shared/widgets/widgets.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateCapexApprovalRequestForm extends StatelessWidget {
  const UpdateCapexApprovalRequestForm({super.key});

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return BlocBuilder<UpdateCapexApprovalRequestCubit,
        UpdateCapexApprovalRequestState>(
      buildWhen: (_, __) => false,
      builder: (context, state) {
        final capex = state.entity;

        if (capex == null) {
          assert(false);

          return const SizedBox.shrink();
        }

        return WideScreenFormWrapper(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: ListView(
              children: [
                if (state.extension.action !=
                    UpdateCapexAction.request_approval) ...[
                  if (capex.approvalLastUser != null)
                    FLLabeledTextVertical(
                      label: tr(
                        Labels.approvals.capex_request.update.last_approver,
                      ),
                      text: capex.approvalLastUser!,
                    ),
                  if (capex.lastDate != null)
                    FLLabeledTextVertical(
                      label: tr(
                        Labels.approvals.capex_request.update.last_date,
                      ),
                      text: df(context, capex.lastDate, mode: 'DT'),
                    ),
                  if (capex.approvalLastComment?.isNotEmpty ?? false)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10,
                      ),
                      child: StatusNotification.message(
                        title: capex.approvalLastComment!,
                        text: tr(
                          Labels.approvals.capex_request.update.last_comment,
                        ),
                      ),
                    ),
                  if (capex.approvalLastUser != null ||
                      capex.lastDate != null ||
                      (capex.approvalLastComment?.isNotEmpty ?? false))
                    const FLDivider(
                      topPadding: 16,
                      bottomPadding: 16,
                    ),
                ],
                FLLabeledTextVertical(
                  label:
                      tr(Labels.approvals.capex_request.update.approval_level),
                  text: tr(
                    Labels.approvals.capex_request.update.approval_level_text,
                    arguments: {
                      'level': '${capex.level ?? '0'}',
                      'amount': $(
                        context,
                        capex.levelAmount ?? 0,
                        currency: capex.divisionCurrency,
                      ),
                    },
                  ),
                ),
                FLGaps.xxs,
                if (state.extension.action !=
                    UpdateCapexAction.request_approval)
                  UpdateCapexApprovalRequestCommentField(
                    initialValue: state.extension.comment,
                  ),
                BlocBuilder<UpdateCapexApprovalRequestCubit,
                    UpdateCapexApprovalRequestState>(
                  buildWhen: (previous, current) =>
                      previous.extension.nextApprover !=
                      current.extension.nextApprover,
                  builder: (context, state) {
                    final nextLevel = capex.nextLevel ?? 0;

                    if (state.extension.action == UpdateCapexAction.approve &&
                        ((state.extension.isLegacyApprovalEngine &&
                                capex.nextLevel == 0) ||
                            capex.nextLevel == null)) {
                      return const SizedBox.shrink();
                    }

                    if (state.extension.action == UpdateCapexAction.decline) {
                      return const SizedBox.shrink();
                    }

                    final approver = state.extension.nextApprover;

                    final id = capex.requestId;
                    final type = capex.requestType;

                    return ApproverSelector.labeled(
                      label: tr(
                        state.extension.action ==
                                UpdateCapexAction.request_approval
                            ? Labels
                                .approvals.capex_request.update.approver_label
                            : Labels.approvals.capex_request.update
                                .next_approver_label,
                      ),
                      approver: approver,
                      config: ApproverLookupConfig(
                        id: id,
                        type: type.name,
                        level: nextLevel,
                        overwriteDivisionId: capex.divisionId,
                        overwriteCostCenterId: capex.costCenterId,
                      ),
                      onApproverSelected: (approver) {
                        context
                            .read<UpdateCapexApprovalRequestCubit>()
                            .updateApprover(approver);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
