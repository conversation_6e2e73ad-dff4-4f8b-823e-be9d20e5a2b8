import 'package:app/approvals/cubits/update_capex_approval_request/cubit.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/shared/helpers/helpers.dart';
import 'package:app/shared/widgets/wrapper/disabled_builder_v3.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateCapexApprovalRequestBottomBar extends StatelessWidget {
  const UpdateCapexApprovalRequestBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return BlocBuilder<UpdateCapexApprovalRequestCubit,
        UpdateCapexApprovalRequestState>(
      buildWhen: (previous, current) =>
          previous.entity != current.entity ||
          previous.isOnline != current.isOnline ||
          previous.extension.nextApprover != current.extension.nextApprover ||
          previous.extension.comment != current.extension.comment,
      builder: (context, state) {
        final capex = state.entity;

        if (capex == null) {
          return const SizedBox.shrink();
        }

        switch (state.extension.action) {
          case UpdateCapexAction.approve:
            return DisabledBuilderV3(
              conditions: [
                DisableCondition(
                  condition: !state.isOnline,
                  message: tr(
                    Labels.info.online_mode_only,
                  ),
                ),
                DisableCondition(
                  condition: !capex.canBeAuthorized,
                  message: tr(
                    Labels.approvals.capex_request.update.can_not_be_authorized,
                  ),
                ),
                DisableCondition(
                  condition: capex.approverId != state.extension.userId,
                  message: tr(
                    Labels
                        .approvals.capex_request.update.user_can_not_authorize,
                  ),
                ),
                DisableCondition(
                  condition: (capex.nextLevel ?? 0) > 0 &&
                      state.extension.nextApprover == null,
                  message: tr(
                    Labels
                        .approvals.capex_request.update.please_select_approver,
                  ),
                ),
              ],
              builder: (context, isDisabled) {
                return FLBottomBar.flat(
                  child: FLFilledButton.text(
                    text:
                        tr(Labels.approvals.capex_request.approve_button_label),
                    onPressed: isDisabled
                        ? null
                        : context
                            .read<UpdateCapexApprovalRequestCubit>()
                            .approve,
                  ),
                );
              },
            );

          case UpdateCapexAction.decline:
            return DisabledBuilderV3(
              conditions: [
                DisableCondition(
                  condition: !state.isOnline,
                  message: tr(
                    Labels.info.online_mode_only,
                  ),
                ),
                DisableCondition(
                  condition: !capex.canBeAuthorized,
                  message: tr(
                    Labels.approvals.capex_request.update.can_not_be_authorized,
                  ),
                ),
                DisableCondition(
                  condition: capex.approverId != state.extension.userId,
                  message: tr(
                    Labels
                        .approvals.capex_request.update.user_can_not_authorize,
                  ),
                ),
                DisableCondition(
                  condition: state.extension.comment?.isEmpty ?? true,
                  message: tr(
                    Labels.approvals.capex_request.update.please_add_comment,
                  ),
                ),
              ],
              builder: (context, isDisabled) {
                return FLBottomBar.flat(
                  child: FLFilledButton.text(
                    text:
                        tr(Labels.approvals.capex_request.decline_button_label),
                    onPressed: isDisabled
                        ? null
                        : context
                            .read<UpdateCapexApprovalRequestCubit>()
                            .decline,
                  ),
                );
              },
            );

          case UpdateCapexAction.request_approval:
            return DisabledBuilderV3(
              conditions: [
                DisableCondition(
                  condition: !state.isOnline,
                  message: tr(
                    Labels.info.online_mode_only,
                  ),
                ),
                DisableCondition(
                  condition: !capex.canBeRequested,
                  message: tr(
                    Labels.approvals.capex_request.update.can_not_be_requested,
                  ),
                ),
                DisableCondition(
                  condition: state.extension.nextApprover == null,
                  message: tr(
                    Labels
                        .approvals.capex_request.update.please_select_approver,
                  ),
                ),
              ],
              builder: (context, isDisabled) {
                return FLBottomBar.flat(
                  child: FLFilledButton.text(
                    text: tr(
                      Labels.approvals.capex_request
                          .request_approval_button_label,
                    ),
                    onPressed: isDisabled
                        ? null
                        : () {
                            Dialogs.showConfirmationDialogV3(
                              title: tr(
                                Labels.approvals.capex_request.update
                                    .request_approval_confirmation.title,
                              ),
                              cancelLabel: tr(Labels.action.cancel),
                              confirmLabel: tr(
                                Labels.approvals.capex_request.update
                                    .request_approval_confirmation.yes,
                              ),
                              onConfirm: context
                                  .read<UpdateCapexApprovalRequestCubit>()
                                  .requestApproval,
                              context: context,
                            );
                          },
                  ),
                );
              },
            );
        }
      },
    );
  }
}
