import 'package:app/approvals/cubits/purchase_requests/cubit.dart';
import 'package:app/approvals/models/filter/purchase_requests_config.dart';
import 'package:app/approvals/widgets/purchase_requests/purchase_requests/list.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/receivings_module/widgets/filter/advanced.dart';
import 'package:app/receivings_module/widgets/filter/app_bar.dart';
import 'package:app/receivings_module/widgets/filter/chips.dart';
import 'package:app/shared/cubits/api_connectivity/cubit.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/helpers/helpers.dart';
import 'package:app/shared/models/user_auth.dart';
import 'package:app/shared/repositories/purchase_requests.dart';
import 'package:app/shared/screens/app_menu.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/widgets/status/connection_status.dart';
import 'package:app/shared/widgets/wide_screen_wrapper.dart';
import 'package:app/shared/widgets/wrapper/api_connectivity_listener.dart';
import 'package:app/shared/widgets/wrapper/filter_listener.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PurchaseRequestsScreen extends StatelessWidget {
  const PurchaseRequestsScreen({Key? key}) : super(key: key);

  static MaterialPageRoute<void> route() {
    return MaterialPageRoute<void>(
      settings: const RouteSettings(name: '/purchase_requests'),
      builder: (_) => const PurchaseRequestsScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<FilterCubit>(
          create: (_) => FilterCubit(const PurchaseRequestFilterConfig()),
        ),
        BlocProvider<PurchaseRequestsCubit>(
          create: (context) {
            final preferencesCache = context.read<PreferencesCacheService>();

            final division = preferencesCache.getField(
              PreferencesField.division,
            );

            return PurchaseRequestsCubit(
              context.read<PurchaseRequestsRepository>(),
              userId: preferencesCache.getField(PreferencesField.user).userId,
              defaultFilterValues:
                  context.read<FilterCubit>().state.config.defaultFilterValues,
              isOnline: context.read<ApiConnectivityCubit>().state,
              language: context.read<LocalizationState>().locale.toString(),
              canUserCreateOrder:
                  context.read<PermissionChecker>().checkPermission(
                        PermissionType.CAN_CREATE_ORDER,
                      ),
              crossPropertyPRApprovalEnabled:
                  division.isCrossPropertyApprovalEnabled,
            )..load();
          },
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          FilterListener(
            onFilterValuesUpdated: (context, state) => context
                .read<PurchaseRequestsCubit>()
                .updateFilterValues(state.values),
          ),
          ApiConnectivityListener(
            onConnectionStateChanged: (context, state) =>
                context.read<PurchaseRequestsCubit>().updateNetworkState(state),
          ),
        ],
        child: const PurchaseRequestsScreenView(),
      ),
    );
  }
}

class PurchaseRequestsScreenView extends StatelessWidget {
  const PurchaseRequestsScreenView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AppMenu(),
      endDrawer: const AdvancedFilter(),
      endDrawerEnableOpenDragGesture: false,
      bottomNavigationBar: const ConnectionStatus(),
      appBar: FreeTextFilterAppBar(
        title: Text(
          tr(context, Labels.navigation.purchase_requests),
        ),
      ),
      body: const WideScreenWrapper(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: <Widget>[
            FilterChips(
              sortingAvailable: true,
              advancedFilteringAvailable: true,
            ),
            Expanded(
              child: PurchaseRequestsList(),
            ),
          ],
        ),
      ),
    );
  }
}
