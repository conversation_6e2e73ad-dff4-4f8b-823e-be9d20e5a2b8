import 'package:app/approvals/models/approval_request.dart';
import 'package:app/approvals/models/approver.dart';
import 'package:app/shared/cubits/single_entity/cubit.dart';
import 'package:app/shared/repositories/capex.dart';
import 'package:app/shared/types/types.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

enum UpdateCapexAction {
  request_approval,
  approve,
  decline,
}

enum UpdateCapexApprovalRequestStatus {
  idle,
  operation_in_progress,
  operation_error,
  request_success,
  approve_success,
  decline_success,
}

@freezed
class UpdateCapexApprovalRequestStateExtension
    with _$UpdateCapexApprovalRequestStateExtension {
  factory UpdateCapexApprovalRequestStateExtension({
    required UpdateCapexApprovalRequestStatus status,
    required String language,
    required String requestId,
    required String divisionId,
    required String userId,
    required bool isLegacyApprovalEngine,
    required UpdateCapexAction action,
    ApproverModel? nextApprover,
    String? comment,
  }) = _UpdateCapexApprovalRequestStateExtension;
}

typedef UpdateCapexApprovalRequestState = SingleEntityState<
    CapexApprovalRequestModel, UpdateCapexApprovalRequestStateExtension>;

class UpdateCapexApprovalRequestCubit extends SingleEntityCubit<
    CapexApprovalRequestModel, UpdateCapexApprovalRequestStateExtension> {
  final CapexRepository _repository;

  UpdateCapexApprovalRequestCubit(
    this._repository, {
    required String requestId,
    required String divisionId,
    required String userId,
    required String language,
    required UpdateCapexAction action,
    required bool isLegacyApprovalEngine,
    required super.isOnline,
  }) : super(
          initialStateExtension: UpdateCapexApprovalRequestStateExtension(
            status: UpdateCapexApprovalRequestStatus.idle,
            language: language,
            userId: userId,
            requestId: requestId,
            divisionId: divisionId,
            isLegacyApprovalEngine: isLegacyApprovalEngine,
            action: action,
          ),
        );

  @override
  Future<Result<CapexApprovalRequestModel, AppError>> loadEntity() =>
      _repository.getOneApprovalRequestById(
        approvalRequestId: state.extension.requestId,
        divisionId: state.extension.divisionId,
        language: state.extension.language,
      );

  void updateApprover(ApproverModel approver) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          nextApprover: approver,
        ),
      ),
    );
  }

  void updateComment(String comment) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          comment: comment.isEmpty ? null : comment,
        ),
      ),
    );
  }

  Future<void> requestApproval() async {
    if (!state.isOnline) {
      return;
    }

    final approver = state.extension.nextApprover;
    final capex = state.entity;

    if (approver == null || capex == null) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          status: UpdateCapexApprovalRequestStatus.operation_in_progress,
        ),
      ),
    );

    final result = await _repository.requestApproval(
      approvalRequestId: capex.requestId,
      capexDivisionId: capex.divisionId,
      approverId: approver.id,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.request_success,
            ),
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.operation_error,
            ),
            error: error,
          ),
        );
      },
    );
  }

  Future<void> approve() async {
    if (!state.isOnline) {
      return;
    }

    final capex = state.entity;

    if (capex == null) {
      return;
    }

    final nextLevel = capex.nextLevel ?? 0;
    final approver = state.extension.nextApprover;

    if (nextLevel > 0 && approver == null) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          status: UpdateCapexApprovalRequestStatus.operation_in_progress,
        ),
      ),
    );

    final result = await _repository.approve(
      approvalRequestId: capex.requestId,
      capexDivisionId: capex.divisionId,
      nextApproverId: approver?.id,
      comment: state.extension.comment,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.approve_success,
            ),
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.operation_error,
            ),
            error: error,
          ),
        );
      },
    );
  }

  Future<void> decline() async {
    if (!state.isOnline) {
      return;
    }

    final capex = state.entity;

    if (capex == null) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          status: UpdateCapexApprovalRequestStatus.operation_in_progress,
        ),
      ),
    );

    final result = await _repository.decline(
      approvalRequestId: capex.requestId,
      capexDivisionId: capex.divisionId,
      comment: state.extension.comment,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.decline_success,
            ),
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              status: UpdateCapexApprovalRequestStatus.operation_error,
            ),
            error: error,
          ),
        );
      },
    );
  }
}
