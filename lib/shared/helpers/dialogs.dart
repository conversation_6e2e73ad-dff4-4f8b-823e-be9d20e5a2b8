import 'dart:math';

import 'package:app/i18n/labels_config.dart';
import 'package:app/ordering_module/widgets/text_dialog.dart';
import 'package:app/shared/config/config.dart';
import 'package:app/shared/helpers/device_utils.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/models/quantity_bottom_sheet_result.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/widgets/dialogs/discount_input_bottom_sheet.dart';
import 'package:app/shared/widgets/dialogs/multi_line_text_editing_bottom_sheet_page.dart';
import 'package:app/shared/widgets/dialogs/single_line_text_editing_bottom_sheet_page.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:app/shared/widgets/quantity_bottom_sheet/calc.dart';
import 'package:app/shared/widgets/quantity_bottom_sheet/calc_v3.dart';
import 'package:app/shared/widgets/quantity_bottom_sheet/regular.dart';
import 'package:app/shared/widgets/quantity_bottom_sheet/regular_v3.dart';
import 'package:app/shared/widgets/quantity_bottom_sheet/stt.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';

class Dialogs {
  static Future<dynamic> show({
    required Widget child,
    required BuildContext context,
    bool barrierDismissible = false,
    String routeName = '/custom_dialog',
  }) async {
    final wrapped = barrierDismissible
        ? child
        : PopScope(
            canPop: false,
            child: child,
          );

    return showDialog(
      context: context,
      builder: (context) => wrapped,
      barrierDismissible: barrierDismissible,
      routeSettings: RouteSettings(name: routeName),
    );
  }

  static Future<dynamic> showV3({
    required Widget child,
    required BuildContext context,
    RouteSettings? routeSettings,
  }) async {
    return FLDialog.show(
      context: context,
      dialog: child,
      routeSettings: routeSettings ??
          const RouteSettings(
            name: '/custom_dialog',
          ),
    );
  }

  static Future<void> showConfirmationDialog({
    required String content,
    required String cancelLabel,
    required String confirmLabel,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    required BuildContext context,
    bool force = false,
  }) async {
    final navigator = Navigator.of(context);

    final isConfirmationDialogsEnabled =
        context.read<PreferencesCacheService>().getField(
              PreferencesField.isConfirmationDialogsEnabled,
            );

    if (!isConfirmationDialogsEnabled && !force) {
      onConfirm();
      return;
    }

    return show(
      context: context,
      routeName: '/confirmation_dialog',
      child: AlertDialog(
        content: Text(content),
        actions: [
          TextButton(
            child: Text(cancelLabel),
            onPressed: () {
              navigator.pop();

              onCancel?.call();
            },
          ),
          ElevatedButton(
            child: Text(confirmLabel),
            onPressed: () {
              navigator.pop();

              onConfirm();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showConfirmationDialogV3({
    required String title,
    Widget? body,
    required String cancelLabel,
    required String confirmLabel,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    required BuildContext context,
    bool force = false,
  }) async {
    final navigator = Navigator.of(context);

    final isConfirmationDialogsEnabled =
        context.read<PreferencesCacheService>().getField(
              PreferencesField.isConfirmationDialogsEnabled,
            );

    if (!isConfirmationDialogsEnabled && !force) {
      onConfirm();
      return;
    }

    return showV3(
      context: context,
      routeSettings: const RouteSettings(name: '/confirmation_dialog'),
      child: FLDialog(
        title: title,
        body: body,
        actions: [
          FLTextButton.gray(
            text: cancelLabel,
            onPressed: () {
              navigator.pop();

              onCancel?.call();
            },
          ),
          FLOutlinedButton.text(
            text: confirmLabel,
            onPressed: () {
              navigator.pop();

              onConfirm();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showDeleteConfirmationDialogV3({
    required String title,
    Widget? body,
    required String cancelLabel,
    required String confirmLabel,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    required BuildContext context,
  }) async {
    final navigator = Navigator.of(context);

    return showV3(
      context: context,
      routeSettings: const RouteSettings(name: '/delete_confirmation_dialog'),
      child: FLDialog(
        title: title,
        body: body,
        actions: [
          FLTextButton.gray(
            text: cancelLabel,
            onPressed: () {
              navigator.pop();

              onCancel?.call();
            },
          ),
          FLOutlinedButton.text(
            text: confirmLabel,
            color: (colors) => colors.redDark,
            onPressed: () {
              navigator.pop();

              onConfirm();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showMaterialBanner({
    required BuildContext context,
    required String content,
    required String dismissLabel,
    required String actionLabel,
    VoidCallback? onDismiss,
    required VoidCallback onAction,
  }) async {
    final messenger = ScaffoldMessenger.maybeOf(context);

    if (messenger == null) {
      return;
    }

    messenger.showMaterialBanner(
      MaterialBanner(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        forceActionsBelow: true,
        content: Text(content),
        actions: <Widget>[
          TextButton(
            child: Text(dismissLabel),
            onPressed: () {
              messenger.hideCurrentMaterialBanner();
              onDismiss?.call();
            },
          ),
          TextButton(
            child: Text(actionLabel),
            onPressed: () {
              messenger.removeCurrentMaterialBanner();
              onAction();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showErrorDialog({
    required AppError error,
    required BuildContext context,
    VoidCallback? onCancel,
    VoidCallback? onRetry,
    VoidCallback? onRefresh,
  }) async {
    final tr = getTranslator(context);

    return show(
      context: context,
      routeName: '/error_dialog',
      child: AlertDialog(
        title: Text(
          tr(Labels.alert_dialog.error_title),
        ),
        content: Text(
          tr(error.message),
        ),
        actions: [
          TextButton(
            child: Text(
              tr(Labels.alert_dialog.cancel_button_label),
            ),
            onPressed: () {
              Navigator.of(context).pop();
              onCancel?.call();
            },
          ),
          if (error.canRetry && onRetry != null)
            TextButton(
              child: Text(
                tr(Labels.alert_dialog.retry_button_label),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
            ),
          if (error.canRefresh && onRefresh != null)
            TextButton(
              child: Text(
                tr(Labels.alert_dialog.refresh_button_label),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                onRefresh();
              },
            ),
        ],
      ),
    );
  }

  static Future<void> showMessageDialog({
    required String message,
    String? title,
    VoidCallback? onClose,
    required BuildContext context,
  }) async {
    return show(
      context: context,
      routeName: '/message_dialog',
      child: AlertDialog(
        title: title != null ? Text(title) : null,
        content: SingleChildScrollView(child: Text(message)),
        actions: [
          TextButton(
            child: Text(
              tr(
                context,
                Labels.alert_dialog.close_button_label,
              ),
            ),
            onPressed: () {
              Navigator.of(context).pop();
              onClose?.call();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showMessageDialogV3({
    required BuildContext context,
    required String? title,
    required String message,
    required String closeLabel,
    VoidCallback? onClose,
  }) {
    final navigator = Navigator.of(context);

    return showV3(
      context: context,
      routeSettings: const RouteSettings(name: '/message_dialog'),
      child: FLDialog(
        title: title,
        body: Text(message),
        actions: [
          FLOutlinedButton.text(
            text: closeLabel,
            onPressed: () {
              navigator.pop();
              onClose?.call();
            },
          ),
        ],
      ),
    );
  }

  static Future<void> showLoadingDialog(
    BuildContext context, {
    String? title,
  }) {
    if (context.theme.useMaterial3) {
      return showV3(
        context: context,
        routeSettings: const RouteSettings(name: '/loading_dialog'),
        child: FLLoadingDialog(title: title),
      );
    }

    //TODO: remove after redesign
    return show(
      context: context,
      routeName: '/loading_dialog',
      child: AlertDialog(
        content: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const Gap(25),
            Flexible(
              fit: FlexFit.loose,
              child: Text(
                tr(context, Labels.alert_dialog.loading_label),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> showSuccessSign(BuildContext context) async {
    final overlay = Overlay.of(context);

    final entry = OverlayEntry(
      builder: (_) => const V3ThemeWrapper(
        child: Center(
          child: FLPulseProgressIndicator.blue(
            symbol: FLSymbol(
              Symbols.check,
            ),
          ),
        ),
      ),
      opaque: false,
    );

    overlay.insert(entry);

    await Future.delayed(
      const Duration(milliseconds: 800),
    );

    entry.remove();
  }

  static void showSingleLineTextFieldDialog({
    required BuildContext context,
    required String initialText,
    required String fieldLabel,
    required String cancelButtonLabel,
    required String submitButtonLabel,
    required void Function(String) onSubmit,
    VoidCallback? onCancel,
    int maxLength = 100,
    bool allowInitialResult = false,
  }) {
    if (context.theme.useMaterial3) {
      showV3(
        context: context,
        routeSettings:
            const RouteSettings(name: '/single_line_text_field_dialog'),
        child: TextDialog(
          initialText: initialText,
          fieldLabel: fieldLabel,
          cancelButtonLabel: cancelButtonLabel,
          submitButtonLabel: submitButtonLabel,
          onSubmit: onSubmit,
          onCancel: onCancel,
          maxLength: maxLength,
          allowInitialResult: allowInitialResult,
        ),
      );

      return;
    }

    show(
      context: context,
      routeName: '/single_line_text_field_dialog',
      child: TextDialog(
        initialText: initialText,
        fieldLabel: fieldLabel,
        cancelButtonLabel: cancelButtonLabel,
        submitButtonLabel: submitButtonLabel,
        onSubmit: onSubmit,
        onCancel: onCancel,
        maxLength: maxLength,
        allowInitialResult: allowInitialResult,
      ),
    );
  }

  static void showSnackBar(
    String content, {
    dynamic action,
    required BuildContext context,
    Duration duration = const Duration(milliseconds: 3000),
  }) {
    if (context.theme.useMaterial3) {
      assert(
        action == null || action is FLSnackBarAction,
        'action is not a FLSnackBarAction',
      );

      ScaffoldMessenger.of(context)
        ..clearSnackBars()
        ..showSnackBar(
          FLSnackBar.message(
            text: content,
            action: action,
            duration: duration,
            theme: context.theme,
          ),
        );

      return;
    }

    assert(
      action == null || action is SnackBarAction,
      'action is not a SnackBarAction',
    );

    ScaffoldMessenger.of(context)
      ..clearSnackBars()
      ..showSnackBar(
        SnackBar(
          margin: DeviceUtils.isPhone(context)
              ? null
              : EdgeInsets.symmetric(
                  vertical: 30,
                  horizontal: max(
                    40,
                    40 +
                        (MediaQuery.of(context).size.width -
                                DeviceUtils.contentMaxWidth) /
                            2,
                  ),
                ),
          content: Text(content),
          behavior: SnackBarBehavior.floating,
          action: action,
          duration: duration,
        ),
      );
  }

  static void showSuccessSnackBar({
    required BuildContext context,
    required String text,
    Duration duration = const Duration(milliseconds: 3000),
  }) {
    ScaffoldMessenger.of(context)
      ..clearSnackBars()
      ..showSnackBar(
        FLSnackBar.success(
          text: text,
          duration: duration,
          theme: Theme.of(context),
        ),
      );
  }

  static void showErrorSnackBar({
    required BuildContext context,
    required String text,
    Duration duration = const Duration(milliseconds: 3000),
  }) {
    ScaffoldMessenger.of(context)
      ..clearSnackBars()
      ..showSnackBar(
        FLSnackBar.error(
          text: text,
          duration: duration,
          theme: Theme.of(context),
        ),
      );
  }

  static void closeOpenDialog(BuildContext context) {
    if (!context.mounted) {
      return;
    }

    if (!(ModalRoute.of(context)?.isCurrent ?? true)) {
      final navigator = Navigator.maybeOf(context);

      if (navigator == null || !navigator.canPop()) {
        return;
      }

      navigator.pop();
    }
  }

  static Future<double?> showRegularQuantityBottomSheet(
    BuildContext context, {
    required QuantityBottomSheetConfig config,
    required String fieldLabel,
    required String buttonLabel,
    String? helperText,
    Widget? additionalInfo,
    final Future<bool> Function(BuildContext context)? confirmDelete,
  }) async {
    final result = await showModalBottomSheet<QuantityBottomSheetResult>(
      context: context,
      routeSettings: const RouteSettings(name: '/regular_qty_bottom_sheet'),
      isScrollControlled: true,
      builder: (internalContext) {
        return Padding(
          padding: MediaQuery.of(internalContext).viewInsets,
          child: RegularQuantityBottomSheet(
            additionalInfo: additionalInfo,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: helperText,
            config: config,
            confirmDelete: confirmDelete,
          ),
        );
      },
    );

    if (result == null) {
      return null;
    }

    if (result.state == QuantityBottomSheetResultState.redirectToTTS &&
        context.mounted) {
      return showTTSQuantityBottomSheet(
        context,
        config: config,
        fieldLabel: fieldLabel,
        buttonLabel: buttonLabel,
        units: helperText ?? '',
        additionalInfo: additionalInfo,
        confirmDelete: confirmDelete,
        fallback: QuantityBottomSheetResultState.redirectToRegular,
      );
    }

    return result.result;
  }

  static Future<double?> showRegularQuantityBottomSheetV3(
    BuildContext context, {
    required QuantityBottomSheetConfig config,
    required String fieldLabel,
    required String buttonLabel,
    String? helperText,
    Widget? additionalInfo,
    bool showHelperButtons = true,
    final Future<bool> Function(BuildContext context)? confirmDelete,
  }) async {
    final result =
        await FLModalBottomSheet.showSinglePage<QuantityBottomSheetResult>(
      context: context,
      routeSettings: const RouteSettings(name: '/regular_qty_bottom_sheet'),
      pageBuilder: (context, controller) {
        return FLModalBottomSheetPage(
          child: RegularQuantityBottomSheetV3(
            additionalInfo: additionalInfo,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: helperText,
            config: config,
            confirmDelete: confirmDelete,
            showHelperButtons: showHelperButtons,
            onResult: (value) => controller.closeWithResult(value),
          ),
        );
      },
    );

    if (result == null) {
      return null;
    }

    if (result.state == QuantityBottomSheetResultState.redirectToTTS &&
        context.mounted) {
      return showTTSQuantityBottomSheet(
        context,
        config: config,
        fieldLabel: fieldLabel,
        buttonLabel: buttonLabel,
        units: helperText ?? '',
        additionalInfo: additionalInfo,
        confirmDelete: confirmDelete,
        fallback: QuantityBottomSheetResultState.redirectToRegular,
      );
    }

    return result.result;
  }

  static Future<double?> showCalcQuantityBottomSheet(
    BuildContext context, {
    required QuantityBottomSheetConfig config,
    required String fieldLabel,
    required String buttonLabel,
    String? helperText,
    Widget? additionalInfo,
    final Future<bool> Function(BuildContext context)? confirmDelete,
  }) async {
    final result = await showModalBottomSheet<QuantityBottomSheetResult>(
      context: context,
      routeSettings: const RouteSettings(name: '/calculator_qty_bottom_sheet'),
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: CalcQuantityBottomSheet(
            additionalInfo: additionalInfo,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: helperText,
            config: config,
            confirmDelete: confirmDelete,
          ),
        );
      },
    );

    if (result == null) {
      return null;
    }

    if (result.state == QuantityBottomSheetResultState.redirectToTTS &&
        context.mounted) {
      return showTTSQuantityBottomSheet(
        context,
        config: config,
        fieldLabel: fieldLabel,
        buttonLabel: buttonLabel,
        units: helperText ?? '',
        additionalInfo: additionalInfo,
        confirmDelete: confirmDelete,
        fallback: QuantityBottomSheetResultState.redirectToCalc,
      );
    }

    return result.result;
  }

  static Future<double?> showCalcQuantityBottomSheetV3(
    BuildContext context, {
    required QuantityBottomSheetConfig config,
    required String fieldLabel,
    required String buttonLabel,
    String? helperText,
    Widget? additionalInfo,
    final Future<bool> Function(BuildContext context)? confirmDelete,
  }) async {
    final result =
        await FLModalBottomSheet.showSinglePage<QuantityBottomSheetResult>(
      context: context,
      routeSettings: const RouteSettings(name: '/calculator_qty_bottom_sheet'),
      pageBuilder: (context, controller) {
        return FLModalBottomSheetPage(
          child: CalcQuantityBottomSheetV3(
            additionalInfo: additionalInfo,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: helperText,
            config: config,
            confirmDelete: confirmDelete,
            onResult: (value) => controller.closeWithResult(value),
          ),
        );
      },
    );

    if (result == null) {
      return null;
    }

    if (result.state == QuantityBottomSheetResultState.redirectToTTS &&
        context.mounted) {
      return showTTSQuantityBottomSheet(
        context,
        config: config,
        fieldLabel: fieldLabel,
        buttonLabel: buttonLabel,
        units: helperText ?? '',
        additionalInfo: additionalInfo,
        confirmDelete: confirmDelete,
        fallback: QuantityBottomSheetResultState.redirectToCalc,
      );
    }

    return result.result;
  }

  static Future<double?> showTTSQuantityBottomSheet(
    BuildContext context, {
    required QuantityBottomSheetConfig config,
    required String fieldLabel,
    required String buttonLabel,
    required String units,
    Widget? additionalInfo,
    final Future<bool> Function(BuildContext context)? confirmDelete,
    final QuantityBottomSheetResultState fallback =
        QuantityBottomSheetResultState.redirectToRegular,
  }) async {
    //TODO: UI V3
    final result = await showModalBottomSheet<QuantityBottomSheetResult>(
      context: context,
      routeSettings: const RouteSettings(name: '/tts_qty_bottom_sheet'),
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: STTQuantityBottomSheet(
            additionalInfo: additionalInfo,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            config: config,
            orderUnit: units,
            confirmDelete: confirmDelete,
            fallback: fallback,
          ),
        );
      },
    );

    if (result == null) {
      return null;
    }

    if (result.state == QuantityBottomSheetResultState.success) {
      return result.result;
    }

    if (!context.mounted) {
      return null;
    }

    switch (result.state) {
      case QuantityBottomSheetResultState.redirectToCalc:
        if (context.theme.useMaterial3) {
          return showCalcQuantityBottomSheetV3(
            context,
            config: config,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: units,
            additionalInfo: additionalInfo,
            confirmDelete: confirmDelete,
          );
        }

        return showCalcQuantityBottomSheet(
          context,
          config: config,
          fieldLabel: fieldLabel,
          buttonLabel: buttonLabel,
          helperText: units,
          additionalInfo: additionalInfo,
          confirmDelete: confirmDelete,
        );

      case QuantityBottomSheetResultState.redirectToRegular:
        if (context.theme.useMaterial3) {
          return showRegularQuantityBottomSheetV3(
            context,
            config: config,
            fieldLabel: fieldLabel,
            buttonLabel: buttonLabel,
            helperText: units,
            showHelperButtons: false,
            additionalInfo: additionalInfo,
            confirmDelete: confirmDelete,
          );
        }

        return showRegularQuantityBottomSheet(
          context,
          config: config,
          fieldLabel: fieldLabel,
          buttonLabel: buttonLabel,
          helperText: units,
          additionalInfo: additionalInfo,
          confirmDelete: confirmDelete,
        );

      case QuantityBottomSheetResultState.redirectToTTS:
      case QuantityBottomSheetResultState.success:
        return null;
    }
  }

  static Future<DateTime?> showDatePickerV3(
    BuildContext context, {
    required DateTime firstDate,
    required DateTime lastDate,
    DateTime? initialDate,
    DateTime? currentDate,
    required String helpText,
    required String cancelText,
    required String confirmText,
  }) async {
    final result = await FLDatePickerDialog.show(
      context: context,
      firstDate: firstDate,
      lastDate: lastDate,
      initialDate: initialDate,
      currentDate: currentDate,
      helpText: helpText,
      cancelText: cancelText,
      confirmText: confirmText,
    );

    if (result == null || !context.mounted) {
      return null;
    }

    return result;
  }

  static Future<DiscountInputResult?> showDiscountUpdateBottomSheet(
    BuildContext context, {
    required double? initialPercentage,
    required double? initialFlatValue,
    required double? maxFlatAmount,
    required String? currency,
    String? title,
  }) {
    return showModalBottomSheet<DiscountInputResult>(
      context: context,
      isScrollControlled: true,
      constraints: const BoxConstraints(
        maxWidth: DeviceUtils.formMaxWidth,
      ),
      builder: (internalContext) {
        return Padding(
          padding: MediaQuery.of(internalContext).viewInsets,
          child: DiscountInputBottomSheet(
            title: title,
            initialPercentage: initialPercentage,
            initialFlatValue: initialFlatValue,
            maxFlatAmount: maxFlatAmount,
            currency: currency,
          ),
        );
      },
    );
  }

  static Future<String?> showSingleLineTextFieldEditingBottomSheet(
    BuildContext context, {
    required String? initialText,
    required String fieldLabel,
    String? title,
    required String submitButtonLabel,
    required int maxLength,
    required bool allowEmptyValue,
    TextInputType? keyboardType,
  }) async {
    final result = await FLModalBottomSheet.showSinglePage<String?>(
      context: context,
      routeSettings:
          const RouteSettings(name: '/single_line_text_field_dialog'),
      pageBuilder: (context, controller) {
        return FLModalBottomSheetPage(
          child: SingleLineTextEditingDialog(
            initialText: initialText,
            controller: controller,
            fieldLabel: fieldLabel,
            title: title,
            maxLength: maxLength,
            submitButtonLabel: submitButtonLabel,
            allowEmptyValue: allowEmptyValue,
            keyboardType: keyboardType,
          ),
        );
      },
    );

    if (result == null || !context.mounted) {
      return null;
    }

    return result;
  }

  static Future<String?> showMultiLineTextEditingBottomSheet(
    BuildContext context, {
    required String? initialText,
    required String fieldLabel,
    int maxLength = 100,
  }) async {
    final result = await FLModalBottomSheet.showSinglePage<String>(
      context: context,
      routeSettings: const RouteSettings(name: '/multi_line_text_field_dialog'),
      pageBuilder: (context, controller) {
        return FLModalBottomSheetPage(
          child: MultiLineTextEditingDialog(
            controller: controller,
            maxLength: maxLength,
            fieldLabel: fieldLabel,
            initialValue: initialText,
          ),
        );
      },
    );

    if (result == null || !context.mounted) {
      return null;
    }

    return result;
  }

  static Future<bool?> showCheckboxBottomSheet(
    BuildContext context,
  ) async {

    // Next children array
    final tr = getTranslator(context);
    final List<Map<String, String>> options = [
      {
        'value': '1752051080', // RECEIVING_ORDERS_ORDER_TYPE_MATCH_ALL
        'label': tr(Labels.receivings.orders.filter.order_type.all),
      },
      {
        'value': '1752051081', // RECEIVING_ORDERS_ORDER_TYPE_MATCH_EXACT
        'label': tr(Labels.receivings.orders.filter.order_type.exact_match),
      },
      {
        'value': '1752051082', // RECEIVING_ORDERS_ORDER_TYPE_MATCH_PARTIAL
        'label': tr(Labels.receivings.orders.filter.order_type.partial),
      },
      {
        'value': '**********', // RECEIVING_ORDERS_ORDER_TYPE_MATCH_RELATED
        'label': tr(Labels.receivings.orders.filter.order_type.related),
      },
    ];

  final selectedValues = <Set<String>>{};

  // add 

  return FLModalBottomSheet.showSinglePage(
    context: context,
    routeSettings: const RouteSettings(name: '/checkbox_bottom_sheet'),
    builder: (context, sheet) {
      return ChangeNotifierProvider<ValueNotifier<bool>>(
        create: (_) => ValueNotifier(false),
        lazy: false,
        child: sheet,
      );
    },
    pageBuilder: (context, controller) {
      return FLSliverModalBottomSheetPage(
        title: const FLModalBottomSheetTitle.regular(
          title: 'Select order type',
          showDivider: true,
        ),
        forceMaxHeight: true,
        enableDrag: false,
        mainContentSlivers: [
          SliverPadding(
            padding: const EdgeInsets.only(
              top: FLSpacings.sm,
              bottom: FLSpacings.sm,
            ),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final child = options[index];

                  return FLFilterListTile.multiSelect(
                    text: child['label']!,
                    selected: selectedValues.contains(child['value']!),
                    onChanged: (selected) {
                      if (selected) {
                        selectedValues.add(child['value']! as Set<String>);
                      } else {
                        selectedValues.remove(child['value']!);
                      }
                    },
                  );
                },
                childCount: options.length,
              ),
            ),
          ),
        ], 
        actionBar: FLModalBottomSheetActionBar.regular(
          text: 'Apply',
          onPressed: () {
            controller.closeWithResult(true);
          },
        ),
      );
    },
  );

  }
}
