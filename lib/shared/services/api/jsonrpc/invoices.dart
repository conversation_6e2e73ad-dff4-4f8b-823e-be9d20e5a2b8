import 'package:app/shared/services/api/client/api_transport.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';

class JsonRpcInvoices {
  static const int _invoiceTransformerRefactoringCapabilityLevel = 20220421;

  final ApiTransport apiTransport;

  JsonRpcInvoices(this.apiTransport);

  Future<Result<Response, AppError>> search({
    required String orgUnitKey,
    required String costCenterKey,
    required String? query,
    required String? invoiceId,
    required String? supplierInvoiceId,
    required String? supplierName,
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return apiTransport.secureCall(
      'Approvals.Invoice.search',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'query': query ?? '',
        'invoiceId': invoiceId ?? '',
        'supplierInvoiceId': supplierInvoiceId ?? '',
        'supplierName': supplierName ?? '',
        'filter': filter,
        'fromDate': fromDate,
        'toDate': toDate,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
        'compatibilityLevel': _invoiceTransformerRefactoringCapabilityLevel,
        'includes': [
          'files_count',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> searchDigitalInvoicesArchive({
    required String orgUnitKey,
    required String costCenterKey,
    required String? query,
    required String? invoiceId,
    required String? supplierInvoiceId,
    required String? supplierName,
    required String filter,
    required String? fromDate,
    required String? toDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) async {
    return apiTransport.secureCall(
      'Invoice.Digital.search',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'query': query ?? '',
        'invoiceId': invoiceId ?? '',
        'supplierInvoiceId': supplierInvoiceId ?? '',
        'supplier': supplierName ?? '',
        'filter': filter,
        'fromDate': fromDate,
        'toDate': toDate,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
        'includes': [
          'files_count',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> getOneById({
    required String orgUnitKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    return apiTransport.secureCall(
      'Approvals.Invoice.getOneById',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
        'includes': [
          if (includeAccountAssignmentRecords) 'account_assignment',
          if (includeLogRecords) 'log',
          if (includeFiles) 'files',
        ],
        'compatibilityLevel': _invoiceTransformerRefactoringCapabilityLevel,
      },
    );
  }

  Future<Result<Response, AppError>> getOneDigitalByIdFromArchive({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    return apiTransport.secureCall(
      'Invoice.Digital.getOneById',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'invoiceId': invoiceId,
        'includes': [
          if (includeAccountAssignmentRecords) 'account_assignment',
          if (includeLogRecords) 'log',
          if (includeFiles) 'files',
        ],
      },
    );
  }

  Future<Result<Response, AppError>> getAccountAssignmentRecords({
    required String orgUnitKey,
    required String costCenterKey,
    required String invoiceId,
  }) async {
    return apiTransport.secureCall(
      'Approvals.Invoice.getAccountAssignmentRecords',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> getLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  }) async {
    return apiTransport.secureCall(
      'Approvals.Invoice.getLogRecords',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> getArchiveLogRecords({
    required String orgUnitKey,
    required String invoiceId,
  }) async {
    return apiTransport.secureCall(
      'Invoice.Log.getRecordsByInvoiceId',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> getDigitalFileUrl({
    required String orgUnitKey,
    required String costCenterKey,
    required String scanningId,
    required String language,
  }) async {
    return apiTransport.secureCall(
      'Invoice.Digital.getFileUrl',
      {
        'orgUnitKey': orgUnitKey,
        'costCenterKey': costCenterKey,
        'scanningId': scanningId,
        'language': language,
      },
    );
  }

  Future<Result<Response, AppError>> requestApproval({
    required String orgUnitKey,
    required String invoiceId,
    required String approverId,
  }) {
    return apiTransport.secureCall(
      'Approvals.Invoice.requestApproval',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
        'nextApproverId': approverId,
      },
    );
  }

  Future<Result<Response, AppError>> reset({
    required String orgUnitKey,
    required String invoiceId,
    required String comment,
  }) {
    return apiTransport.secureCall(
      'Approvals.Invoice.reset',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
        'comment': comment,
      },
    );
  }

  Future<Result<Response, AppError>> approve({
    required String orgUnitKey,
    required String invoiceId,
    String? nextApproverId,
  }) {
    return apiTransport.secureCall(
      'Approvals.Invoice.approve',
      {
        'orgUnitKey': orgUnitKey,
        'invoiceId': invoiceId,
        'nextApproverId': nextApproverId,
      },
    );
  }

  Future<Result<Response, AppError>> getApprovalTrail({
    required String divisionId,
    required String invoiceId,
  }) {
    return apiTransport.secureCall(
      'Approvals.Invoice.getApprovalTrail',
      {
        'orgUnitKey': divisionId,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> approveDigital({
    required String divisionId,
    required String invoiceId,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.approve',
      {
        'orgUnitKey': divisionId,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> getDigitalProcessingList({
    required String divisionId,
    required String? startDate,
    required String? endDate,
    required String orderByField,
    required String orderType,
    required int page,
    required int pageSize,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.Processing.getList',
      {
        'orgUnitKey': divisionId,
        'startDate': startDate,
        'endDate': endDate,
        'orderByField': orderByField,
        'orderType': orderType,
        'page': page,
        'pageSize': pageSize,
      },
    );
  }

  Future<Result<Response, AppError>> getOneDigitalProcessingById({
    required String divisionId,
    required String scanningId,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.Processing.getOneByScanningId',
      {
        'orgUnitKey': divisionId,
        'scanningId': scanningId,
      },
    );
  }

  Future<Result<Response, AppError>> getDigitalInvoiceAttachments({
    required String divisionId,
    required String costCenterId,
    required String invoiceId,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.Attachment.getAll',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'invoiceId': invoiceId,
      },
    );
  }

  Future<Result<Response, AppError>> addDigitalInvoiceAttachment({
    required String divisionId,
    required String costCenterId,
    required String invoiceId,
    required String fileName,
    required String base64Data,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.Attachment.add',
      {
        'orgUnitKey': divisionId,
        'costCenterKey': costCenterId,
        'invoiceId': invoiceId,
        'document': {
          'file_name': fileName,
          'base64_binary_data': base64Data,
        },
      },
    );
  }

  Future<Result<Response, AppError>> getDigitalInvoiceTaxBreakdown({
    required String divisionId,
    required String invoiceId,
  }) {
    return apiTransport.secureCall(
      'Invoice.Digital.getBreakdownByTax',
      {
        'orgUnitKey': divisionId,
        'invoiceId': invoiceId,
      },
    );
  }
}
