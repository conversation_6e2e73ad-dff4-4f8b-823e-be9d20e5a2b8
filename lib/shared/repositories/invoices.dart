import 'package:app/approvals/models/approval_trail_item.dart';
import 'package:app/approvals/models/filter/invoices_config.dart';
import 'package:app/approvals/models/log_record.dart';
import 'package:app/invoices_archive_module/models/account_assignment_record.dart';
import 'package:app/invoices_archive_module/models/filter/archived_invoices_config.dart';
import 'package:app/invoices_archive_module/models/invoice.dart';
import 'package:app/invoices_archive_module/models/scanning_file.dart';
import 'package:app/invoices_archive_module/models/tax_breakdown_totals.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/scanner_module/models/invoice_processing_record.dart';
import 'package:app/shared/config/config.dart';
import 'package:app/shared/repositories/repositories.dart';
import 'package:app/shared/services/api/api.dart';
import 'package:app/shared/services/api/response.dart';
import 'package:app/shared/services/preferences/cache.dart';
import 'package:app/shared/services/preferences/field.dart';
import 'package:app/shared/types/types.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class InvoicesRepository extends ApiRepository {
  InvoicesRepository({
    required this.preferencesCacheService,
    required ApiService apiService,
  }) : super(
          apiService,
        );

  final PreferencesCacheService preferencesCacheService;

  Future<Result<List<InvoiceModel>, AppError>> search({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    try {
      late final String filter;

      switch (filterValues[INVOICES_FILTER_STATUS_FID]!.value) {
        case INVOICES_FILTER_STATUS_ASSIGNED_TO_ME:
          filter = 'ASSIGNED_TO_ME';
          break;

        case INVOICES_FILTER_STATUS_READY_TO_APPROVE:
          filter = 'READY_TO_APPROVE';
          break;

        default:
          assert(false);
          break;
      }

      late final String orderByField;
      late final String orderType;

      switch (filterValues[INVOICES_SORTING_FID]!.value) {
        case INVOICES_SORT_BY_INVOICE_DATE_ASC:
          orderByField = 'invoice_date';
          orderType = 'ASC';
          break;

        case INVOICES_SORT_BY_INVOICE_DATE_DESC:
          orderByField = 'invoice_date';
          orderType = 'DESC';
          break;

        case INVOICES_SORT_BY_REQUESTED_AT_ASC:
          orderByField = 'requested_at';
          orderType = 'ASC';
          break;

        case INVOICES_SORT_BY_REQUESTED_AT_DESC:
          orderByField = 'requested_at';
          orderType = 'DESC';
          break;

        case INVOICES_SORT_BY_UPLOADED_AT_ASC:
          orderByField = 'uploaded_at';
          orderType = 'ASC';
          break;

        case INVOICES_SORT_BY_UPLOADED_AT_DESC:
          orderByField = 'uploaded_at';
          orderType = 'DESC';
          break;

        case INVOICES_SORT_BY_SUPPLIER_NAME_ASC:
          orderByField = 'supplier_name';
          orderType = 'ASC';
          break;

        case INVOICES_SORT_BY_SUPPLIER_NAME_DESC:
          orderByField = 'supplier_name';
          orderType = 'DESC';
          break;

        default:
          assert(false);
          break;
      }

      final DateTimeRange? range = filterValues[INVOICES_DATE_RANGE_FID]!.value;
      final df = DateFormat('dd.MM.yyyy');

      final fromDate = range != null ? df.format(range.start) : null;
      final toDate = range != null ? df.format(range.end) : null;

      final String query = filterValues[INVOICES_FREE_TEXT_FID]!.value;

      final String invoiceId = filterValues[INVOICES_INVOICE_ID_FID]!.value;
      final String supplierInvoiceId =
          filterValues[INVOICES_SUPPLIER_INVOICE_ID_FID]!.value;
      final String supplierName =
          filterValues[INVOICES_SUPPLIER_NAME_FID]!.value;

      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchInvoices(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        query: query,
        invoiceId: invoiceId,
        supplierInvoiceId: supplierInvoiceId,
        supplierName: supplierName,
        filter: filter,
        fromDate: fromDate,
        toDate: toDate,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(InvoiceModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<InvoiceModel>, AppError>> searchDigitalArchive({
    required Map<String, FilterValueModel> filterValues,
    required int page,
    required int pageSize,
    required String language,
  }) async {
    try {
      late final String filter;

      switch (filterValues[INVOICES_ARCHIVE_FILTER_STATUS_FID]!.value) {
        case INVOICES_ARCHIVE_FILTER_STATUS_OPEN:
          filter = 'OPEN';
          break;

        case INVOICES_ARCHIVE_FILTER_STATUS_ASSIGNED_TO_ME:
          filter = 'ASSIGNED_TO_ME';
          break;

        case INVOICES_ARCHIVE_FILTER_STATUS_IN_APPROVAL:
          filter = 'IN_APPROVAL';
          break;

        case INVOICES_ARCHIVE_FILTER_STATUS_READY_TO_APPROVE:
          filter = 'READY_TO_APPROVE';
          break;

        case INVOICES_ARCHIVE_FILTER_STATUS_READY_TO_TRANSFER_TO_ACCOUNTING:
          filter = 'READY_TO_TRANSFER_TO_ACCOUNTING';
          break;

        case INVOICES_ARCHIVE_FILTER_STATUS_SEPA_PAYMENT:
          filter = 'SEPA_PAYMENT';
          break;

        default:
          assert(false);
          break;
      }

      late final String orderByField;
      late final String orderType;

      switch (filterValues[INVOICES_ARCHIVE_SORTING_FID]!.value) {
        case INVOICES_ARCHIVE_SORT_BY_INVOICE_DATE_DESC:
          orderByField = 'invoice_date';
          orderType = 'DESC';
          break;

        case INVOICES_ARCHIVE_SORT_BY_INVOICE_DATE_ASC:
          orderByField = 'invoice_date';
          orderType = 'ASC';
          break;

        case INVOICES_ARCHIVE_SORT_BY_SUPPLIER_NAME_DESC:
          orderByField = 'supplier_name';
          orderType = 'DESC';
          break;

        case INVOICES_ARCHIVE_SORT_BY_SUPPLIER_NAME_ASC:
          orderByField = 'supplier_name';
          orderType = 'ASC';
          break;

        default:
          assert(false);
          break;
      }

      final DateTimeRange? range =
          filterValues[INVOICES_ARCHIVE_DATE_RANGE_FID]!.value;
      final df = DateFormat('dd.MM.yyyy');

      final fromDate = range != null ? df.format(range.start) : null;
      final toDate = range != null ? df.format(range.end) : null;

      final divisionId =
          preferencesCacheService.getField(PreferencesField.division).id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.searchDigitalInvoicesArchive(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        query: filterValues[INVOICES_ARCHIVE_FREE_TEXT_FID]!.value,
        filter: filter,
        fromDate: fromDate,
        toDate: toDate,
        orderByField: orderByField,
        orderType: orderType,
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(InvoiceModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<InvoiceModel, AppError>> getById({
    required String invoiceId,
    required String invoiceDivisionId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    try {
      Result<Response, AppError> result = await api.getOneInvoiceById(
        orgUnitKey: invoiceDivisionId,
        invoiceId: invoiceId,
        includeAccountAssignmentRecords: includeAccountAssignmentRecords,
        includeLogRecords: includeLogRecords,
        includeFiles: includeFiles,
      );

      return result.match(
        ok: (response) => Ok(
          InvoiceModel.fromJson(response.toMap()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<InvoiceModel, AppError>> getDigitalByIdFromArchive({
    required String invoiceId,
    String? invoiceDivisionId,
    bool includeAccountAssignmentRecords = false,
    bool includeLogRecords = false,
    bool includeFiles = false,
  }) async {
    try {
      final divisionId = invoiceDivisionId ??
          preferencesCacheService
              .getField(
                PreferencesField.division,
              )
              .id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getOneDigitalInvoiceByIdFromArchive(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        invoiceId: invoiceId,
        includeAccountAssignmentRecords: includeAccountAssignmentRecords,
        includeLogRecords: includeLogRecords,
        includeFiles: includeFiles,
      );

      return result.match(
        ok: (response) => Ok(
          InvoiceModel.fromJson(response.toMap()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<LogRecordModel>, AppError>> getLogRecords({
    required String invoiceId,
    required String invoiceDivisionId,
    bool fromArchive = false,
  }) async {
    try {
      final Result<Response, AppError> result;

      if (fromArchive) {
        result = await api.getArchiveInvoiceLogRecords(
          orgUnitKey: invoiceDivisionId,
          invoiceId: invoiceId,
        );
      } else {
        result = await api.getInvoiceLogRecords(
          orgUnitKey: invoiceDivisionId,
          invoiceId: invoiceId,
        );
      }

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(LogRecordModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<AccountAssignmentRecordModel>, AppError>>
      getAccountAssignmentRecords(String invoiceId) async {
    try {
      final divisionId = preferencesCacheService
          .getField(
            PreferencesField.division,
          )
          .id;
      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getInvoiceAccountAssignmentRecords(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        invoiceId: invoiceId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(AccountAssignmentRecordModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<String?, AppError>> getDigitalInvoiceFileUrl(
    String scanningId, {
    required String language,
    String? invoiceDivisionId,
  }) async {
    try {
      final divisionId = invoiceDivisionId ??
          preferencesCacheService
              .getField(
                PreferencesField.division,
              )
              .id;

      final costCenterId =
          preferencesCacheService.getField(PreferencesField.costCenterId);

      final result = await api.getDigitalInvoiceFileUrl(
        orgUnitKey: divisionId,
        costCenterKey: costCenterId,
        scanningId: scanningId,
        language: language,
      );

      return result.match(
        ok: (response) {
          final invoiceScanningFile =
              InvoiceScanningFileModel.fromJson(response.getData());

          if (invoiceScanningFile.downloadUrl.startsWith('https://')) {
            return Ok(
              invoiceScanningFile.downloadUrl,
            );
          }

          return Ok(
            '${CommonConfiguration.apiBaseUrl}${invoiceScanningFile.downloadUrl}',
          );
        },
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> requestApproval({
    required String invoiceId,
    required String approverId,
  }) async {
    try {
      final divisionId = preferencesCacheService
          .getField(
            PreferencesField.division,
          )
          .id;

      final result = await api.requestInvoiceApproval(
        orgUnitKey: divisionId,
        invoiceId: invoiceId,
        approverId: approverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> reset({
    required String invoiceId,
    required String comment,
    required String invoiceDivisionId,
  }) async {
    try {
      final result = await api.resetInvoice(
        orgUnitKey: invoiceDivisionId,
        invoiceId: invoiceId,
        comment: comment,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> approve({
    required String invoiceId,
    String? nextApproverId,
    required String invoiceDivisionId,
  }) async {
    try {
      final result = await api.approveInvoice(
        orgUnitKey: invoiceDivisionId,
        invoiceId: invoiceId,
        nextApproverId: nextApproverId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<ApprovalTrailItemModel>, AppError>> getApprovalTrail({
    required String invoiceId,
    required String invoiceDivisionId,
  }) async {
    try {
      final result = await api.getInvoiceApprovalTrail(
        divisionId: invoiceDivisionId,
        invoiceId: invoiceId,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(ApprovalTrailItemModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<bool, AppError>> approveDigital({
    required String invoiceId,
    required String invoiceDivisionId,
  }) async {
    try {
      final result = await api.approveDigitalInvoice(
        divisionId: invoiceDivisionId,
        invoiceId: invoiceId,
      );

      return result.match(
        ok: (_) => const Ok(true),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<List<InvoiceProcessingRecordModel>, AppError>>
      getDigitalInvoicesProcessingList({
    required int page,
    required int pageSize,
  }) async {
    try {
      final divisionId = preferencesCacheService
          .getField(
            PreferencesField.division,
          )
          .id;

      final result = await api.getDigitalInvoicesProcessingList(
        divisionId: divisionId,
        startDate: null,
        endDate: null,
        orderByField: 'uploaded_at',
        orderType: 'DESC',
        page: page,
        pageSize: pageSize,
      );

      return result.match(
        ok: (response) => Ok(
          List<Map<String, dynamic>>.from(response.getData())
              .map(InvoiceProcessingRecordModel.fromJson)
              .toList(),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<InvoiceProcessingRecordModel, AppError>>
      getDigitalInvoicesProcessingById({
    required String scanningId,
  }) async {
    try {
      final divisionId = preferencesCacheService
          .getField(
            PreferencesField.division,
          )
          .id;

      final result = await api.getOneDigitalInvoiceProcessingById(
        divisionId: divisionId,
        scanningId: scanningId,
      );

      return result.match(
        ok: (response) => Ok(
          InvoiceProcessingRecordModel.fromJson(response.getData()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }

  Future<Result<InvoiceTaxBreakdownTotalsModel, AppError>>
      getDigitalInvoiceTaxBreakdown({
    required String invoiceId,
    required String divisionId,
  }) async {
    try {
      final result = await api.getDigitalInvoiceTaxBreakdown(
        divisionId: divisionId,
        invoiceId: invoiceId,
      );

      return result.match(
        ok: (response) => Ok(
          InvoiceTaxBreakdownTotalsModel.fromJson(response.getData()),
        ),
        err: (error) => Err(
          error.copyWith(stackTrace: StackTrace.current),
        ),
      );
    } catch (e, t) {
      return Err(
        AppError(
          code: UNEXPECTED_ERROR_CODE,
          message: UNEXPECTED_ERROR_MESSAGE,
          throwable: e,
          stackTrace: t,
        ),
      );
    }
  }
}
